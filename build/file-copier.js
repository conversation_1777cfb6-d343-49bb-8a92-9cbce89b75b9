const fs = require("fs");
const path = require("path");
// 获取应用程序名称
const packageJson = require("../package.json");
const appName = packageJson.name;

/**
 * 复制示例模板文件
 * @param {string} rootDir - 项目根目录
 * @param {string} exampleDir - 示例目录路径
 */
function copyExampleTemplate(rootDir, exampleDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在复制示例模板...");

  const sourceTemplatePath = path.join(__dirname, "示例模板.xlsx");
  const targetTemplatePath = path.join(exampleDir, "示例模板.xlsx");

  try {
    if (fs.existsSync(sourceTemplatePath)) {
      fs.copyFileSync(sourceTemplatePath, targetTemplatePath);
      console.log("\x1b[32m%s\x1b[0m", "示例模板复制成功");
      return true;
    } else {
      console.log(
        "\x1b[33m%s\x1b[0m",
        "示例模板文件不存在，请手动添加示例文件到示例目录"
      );
      return false;
    }
  } catch (err) {
    console.log("\x1b[31m%s\x1b[0m", `复制示例模板时出错: ${err.message}`);
    return false;
  }
}

/**
 * 创建主README文件
 * @param {string} distDir - 构建目录路径
 */
function createMainReadme(distDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在创建说明文件...");

  try {
    // 读取版本号
    const packageJsonPath = path.join(__dirname, "..", "package.json");
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
    const version = packageJson.version;

    // 读取原始 README.md 文件
    const originalReadmePath = path.join(__dirname, "..", "README.md");
    const originalContent = fs.readFileSync(originalReadmePath, "utf8");

    // 提取用户需要的内容
    const userReadmeContent = extractUserContent(originalContent, version);

    const readmePath = path.join(distDir, "README.txt");
    fs.writeFileSync(readmePath, userReadmeContent, "utf8");

    console.log("\x1b[32m%s\x1b[0m", "主README文件创建成功");
    return true;
  } catch (err) {
    console.log("\x1b[31m%s\x1b[0m", `创建README文件时出错: ${err.message}`);
    return false;
  }
}

/**
 * 从原始 README.md 中提取用户需要的内容
 * @param {string} originalContent - 原始 README.md 内容
 * @param {string} version - 当前版本号
 * @returns {string} 用户版本的 README 内容
 */
function extractUserContent(originalContent, version) {
  const lines = originalContent.split("\n");

  const defaultProgramName = appName;

  // 提取程序名称（从第一行标题）
  const titleMatch = lines[0].match(/^# (.+)$/);
  const programName = titleMatch ? titleMatch[1] : defaultProgramName;

  // 提取程序介绍（从第3行开始的描述）
  const introStart = lines.findIndex(
    (line) => line.trim() && !line.startsWith("#")
  );
  const introEnd = lines.findIndex(
    (line, index) => index > introStart && line.startsWith("##")
  );
  const introduction = lines.slice(introStart, introEnd).join("\n").trim();

  // 提取最终用户使用方法
  const userMethodStart = lines.findIndex((line) =>
    line.includes("## 最终用户使用方法")
  );
  let userMethodEnd = lines.findIndex(
    (line, index) => index > userMethodStart && line.startsWith("##")
  );
  if (userMethodEnd === -1) userMethodEnd = lines.length;

  const userMethods = lines
    .slice(userMethodStart + 1, userMethodEnd)
    .filter((line) => !line.startsWith("##"))
    .join("\n")
    .trim();

  // 提取版本历史
  const versionStart = lines.findIndex((line) => line.includes("## 版本历史"));
  let versionEnd = lines.findIndex(
    (line, index) => index > versionStart && line.startsWith("##")
  );
  if (versionEnd === -1) versionEnd = lines.length;

  const versionHistory = lines
    .slice(versionStart + 1, versionEnd)
    .filter((line) => !line.startsWith("##"))
    .join("\n")
    .trim();

  // 生成用户版本的 README
  return `# ${programName} v${version}

## 程序介绍

${introduction}

## 使用方法

${userMethods}

## 文件夹说明

- input/ - 放置原始清单Excel文件
- output/ - 存放生成的合并清单文件
- json/ - 存储中间处理数据（可选查看）
- 示例/ - 包含示例模板文件

## 注意事项

1. 请确保Excel文件格式正确，包含必要的表头信息
2. 建议在处理前备份原始文件
3. 如遇到问题，请检查Excel文件的格式是否符合标准

## 版本历史

${versionHistory}

---

如有问题或建议，请联系技术支持。
`;
}

/**
 * 复制其他必要文件
 * @param {string} rootDir - 项目根目录
 * @param {string} distDir - 构建目录路径
 */
function copyOtherFiles(rootDir, distDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在复制其他必要文件...");

  const filesToCopy = [
    // 可以在这里添加其他需要复制的文件
    // { source: "LICENSE", target: "LICENSE" },
    // { source: "CHANGELOG.md", target: "CHANGELOG.txt" }
  ];

  let successCount = 0;

  filesToCopy.forEach(({ source, target }) => {
    const sourcePath = path.join(rootDir, source);
    const targetPath = path.join(distDir, target);

    try {
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`\x1b[32m%s\x1b[0m`, `✓ ${source} -> ${target}`);
        successCount++;
      } else {
        console.log(`\x1b[33m%s\x1b[0m`, `⚠ ${source} 文件不存在，跳过复制`);
      }
    } catch (err) {
      console.log(
        `\x1b[31m%s\x1b[0m`,
        `✗ 复制 ${source} 时出错: ${err.message}`
      );
    }
  });

  if (filesToCopy.length > 0) {
    console.log(
      `\x1b[32m%s\x1b[0m`,
      `其他文件复制完成 (${successCount}/${filesToCopy.length})`
    );
  }

  return successCount;
}

/**
 * 验证文件复制结果
 * @param {string} distDir - 构建目录路径
 * @returns {boolean} 验证是否成功
 */
function validateFileCopy(distDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在验证文件复制结果...");

  const requiredFiles = ["README.txt"];

  const optionalFiles = [path.join("示例", "示例模板.xlsx")];

  let allRequiredValid = true;

  // 验证必需文件
  requiredFiles.forEach((fileName) => {
    const filePath = path.join(distDir, fileName);
    if (fs.existsSync(filePath)) {
      console.log(`\x1b[32m%s\x1b[0m`, `✓ ${fileName} 已创建`);
    } else {
      console.log(`\x1b[31m%s\x1b[0m`, `✗ ${fileName} 缺失`);
      allRequiredValid = false;
    }
  });

  // 验证可选文件
  optionalFiles.forEach((fileName) => {
    const filePath = path.join(distDir, fileName);
    if (fs.existsSync(filePath)) {
      console.log(`\x1b[32m%s\x1b[0m`, `✓ ${fileName} 已创建`);
    } else {
      console.log(`\x1b[33m%s\x1b[0m`, `⚠ ${fileName} 未找到（可选）`);
    }
  });

  if (allRequiredValid) {
    console.log("\x1b[32m%s\x1b[0m", "文件复制验证通过");
  } else {
    console.log("\x1b[31m%s\x1b[0m", "文件复制验证失败");
  }

  return allRequiredValid;
}

module.exports = {
  copyExampleTemplate,
  createMainReadme,
  copyOtherFiles,
  validateFileCopy,
};
