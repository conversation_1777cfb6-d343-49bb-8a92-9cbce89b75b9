const fs = require("fs");
const path = require("path");
const os = require("os");

// 获取应用程序版本号和名称
const packageJson = require("../package.json");
const appVersion = packageJson.version;
const appName = packageJson.name;

// 获取当前系统信息
const platform = os.platform();
const isWindows = platform === "win32";
const isMac = platform === "darwin";
const isLinux = platform === "linux";

// 项目根目录
const rootDir = path.dirname(__dirname);

// 构建目录
const distDir = path.join(rootDir, "dist");

/**
 * 确保目录存在
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 获取构建目标平台
 * @returns {Array<string>} 目标平台数组
 */
function getBuildTargets() {
  let targets = [];
  if (isWindows) {
    targets.push("node18-win-x64");
  } else if (isMac) {
    targets.push("node18-macos-x64");
    targets.push("node18-win-x64"); // 在 Mac 上也构建 Windows 版本
  } else if (isLinux) {
    targets.push("node18-linux-x64");
    targets.push("node18-win-x64"); // 在 Linux 上也构建 Windows 版本
  }
  return targets;
}

/**
 * 获取当前日期字符串
 * @returns {string} 格式化的日期字符串 (YYYYMMDD)
 */
function getDateString() {
  const now = new Date();
  return `${now.getFullYear()}${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
}

/**
 * 获取zip文件名
 * @returns {string} zip文件名
 */
function getZipFileName() {
  const dateStr = getDateString();
  return `${appName}_v${appVersion}_${dateStr}.zip`;
}

/**
 * 显示构建开始信息
 */
function showBuildStartMessage() {
  console.log("\x1b[36m%s\x1b[0m", "开始构建可执行文件...");
}

/**
 * 显示构建完成信息
 * @param {string} zipFileName - zip文件名
 * @param {number} fileSizeInMB - 文件大小(MB)
 */
function showBuildCompleteMessage(zipFileName, fileSizeInMB) {
  console.log("\x1b[32m%s\x1b[0m", `构建完成！`);
  console.log(
    `\x1b[32m%s\x1b[0m`,
    `Zip文件已创建: ${zipFileName} (${fileSizeInMB} MB)`
  );
  console.log(`\x1b[36m%s\x1b[0m`, `可执行文件已生成在 ${distDir} 目录下`);
  console.log("\x1b[36m%s\x1b[0m", "已将以下文件打包到zip文件中：");
  console.log(`1. ${appName}.exe`);
  console.log("2. README.txt");
  console.log("3. input/目录 (用于放置原始清单文件)");
  console.log("4. output/目录 (用于存放生成的合并清单文件)");
  console.log("5. json/目录 (用于存储中间处理的JSON数据)");
  console.log("6. 示例/目录 (可选)");
}

/**
 * 显示构建错误信息
 * @param {Error} error - 错误对象
 */
function showBuildErrorMessage(error) {
  console.error("\x1b[31m%s\x1b[0m", "构建过程中出错:");
  console.error(error);
}

module.exports = {
  appVersion,
  appName,
  platform,
  isWindows,
  isMac,
  isLinux,
  rootDir,
  distDir,
  ensureDirectoryExists,
  getBuildTargets,
  getDateString,
  getZipFileName,
  showBuildStartMessage,
  showBuildCompleteMessage,
  showBuildErrorMessage,
};
