const fs = require("fs");
const path = require("path");

/**
 * 创建必要的目录结构
 * @param {string} distDir - 构建目录路径
 */
function createDirectoryStructure(distDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在创建目录结构...");

  // 创建示例目录
  const exampleDir = path.join(distDir, "示例");
  if (!fs.existsSync(exampleDir)) {
    fs.mkdirSync(exampleDir, { recursive: true });
  }

  // 创建input、output和json目录
  console.log("\x1b[33m%s\x1b[0m", "正在创建input、output和json目录...");
  const inputDir = path.join(distDir, "input");
  const outputDir = path.join(distDir, "output");
  const jsonDir = path.join(distDir, "json");

  // 创建所有必要的目录
  [inputDir, outputDir, jsonDir].forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  console.log("\x1b[32m%s\x1b[0m", "目录结构创建完成");
  
  return {
    exampleDir,
    inputDir,
    outputDir,
    jsonDir,
  };
}

/**
 * 创建README文件
 * @param {Object} directories - 目录对象
 */
function createReadmeFiles(directories) {
  const { inputDir, outputDir, jsonDir } = directories;

  console.log("\x1b[33m%s\x1b[0m", "正在创建README文件...");

  // 在input目录中创建一个README.txt文件，说明如何使用
  fs.writeFileSync(
    path.join(inputDir, "README.txt"),
    `请将您的原始清单Excel文件放在此目录下，然后运行程序。
程序将自动处理这些文件，并在output目录下生成合并后的清单文件。`
  );

  // 在output目录中创建一个README.txt文件，说明目录用途
  fs.writeFileSync(
    path.join(outputDir, "README.txt"),
    `程序处理完成后，合并的清单文件将保存在此目录下。`
  );

  // 在json目录中创建一个README.txt文件，说明目录用途
  fs.writeFileSync(
    path.join(jsonDir, "README.txt"),
    `此目录用于存储中间处理的JSON数据文件。`
  );

  console.log("\x1b[32m%s\x1b[0m", "README文件创建完成");
}

/**
 * 验证目录结构
 * @param {string} distDir - 构建目录路径
 * @returns {boolean} 验证是否成功
 */
function validateDirectoryStructure(distDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在验证目录结构...");

  const requiredDirs = [
    "示例",
    "input",
    "output", 
    "json"
  ];

  let allValid = true;

  requiredDirs.forEach(dirName => {
    const dirPath = path.join(distDir, dirName);
    if (fs.existsSync(dirPath)) {
      console.log(`\x1b[32m%s\x1b[0m`, `✓ ${dirName}/ 目录已创建`);
    } else {
      console.log(`\x1b[31m%s\x1b[0m`, `✗ ${dirName}/ 目录缺失`);
      allValid = false;
    }
  });

  // 验证README文件
  const readmeFiles = [
    path.join(distDir, "input", "README.txt"),
    path.join(distDir, "output", "README.txt"),
    path.join(distDir, "json", "README.txt")
  ];

  readmeFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      console.log(`\x1b[32m%s\x1b[0m`, `✓ ${path.relative(distDir, filePath)} 已创建`);
    } else {
      console.log(`\x1b[31m%s\x1b[0m`, `✗ ${path.relative(distDir, filePath)} 缺失`);
      allValid = false;
    }
  });

  if (allValid) {
    console.log("\x1b[32m%s\x1b[0m", "目录结构验证通过");
  } else {
    console.log("\x1b[31m%s\x1b[0m", "目录结构验证失败");
  }

  return allValid;
}

module.exports = {
  createDirectoryStructure,
  createReadmeFiles,
  validateDirectoryStructure,
};
