const path = require("path");
const {
  rootDir,
  distDir,
  ensureDirectoryExists,
  getBuildTargets,
  getZipFileName,
  showBuildStartMessage,
  showBuildCompleteMessage,
  showBuildErrorMessage,
} = require("./config");

const { cleanBuildDirectory, cleanTempFiles } = require("./cleaner");
const { packageApplication, validatePackageResult } = require("./packager");
const {
  createDirectoryStructure,
  createReadmeFiles,
  validateDirectoryStructure,
} = require("./directory-creator");
const {
  copyExampleTemplate,
  createMainReadme,
  copyOtherFiles,
  validateFileCopy,
} = require("./file-copier");
const {
  createZipFile,
  validateZipFile,
  getCompressionStats,
  showCompressionStats,
} = require("./archiver");

/**
 * 执行构建过程
 */
async function executeBuild() {
  try {
    // 显示构建开始信息
    showBuildStartMessage();

    // 确保 dist 目录存在
    ensureDirectoryExists(distDir);

    // 1. 清理之前的构建文件
    cleanTempFiles(rootDir);
    cleanBuildDirectory(distDir);

    // 2. 使用 pkg 打包应用程序
    const targets = getBuildTargets();
    await packageApplication(targets, rootDir);

    // 3. 验证打包结果
    const packageValid = validatePackageResult(distDir, targets);
    if (!packageValid) {
      throw new Error("打包验证失败");
    }

    // 4. 创建目录结构
    const directories = createDirectoryStructure(distDir);
    createReadmeFiles(directories);

    // 5. 验证目录结构
    const dirValid = validateDirectoryStructure(distDir);
    if (!dirValid) {
      throw new Error("目录结构验证失败");
    }

    // 6. 复制文件
    copyExampleTemplate(rootDir, directories.exampleDir);
    createMainReadme(distDir);
    copyOtherFiles(rootDir, distDir);

    // 7. 验证文件复制
    const fileValid = validateFileCopy(distDir);
    if (!fileValid) {
      throw new Error("文件复制验证失败");
    }

    // 8. 创建zip文件
    const zipFileName = getZipFileName();
    const zipFilePath = path.join(rootDir, zipFileName);

    const zipResult = await createZipFile(
      distDir,
      zipFilePath,
      showBuildCompleteMessage,
      (error) => {
        console.error("\x1b[31m%s\x1b[0m", "创建zip文件时出错:");
        console.error(error);
      }
    );

    // 9. 验证zip文件
    const zipValid = validateZipFile(zipFilePath);
    if (!zipValid.valid) {
      throw new Error(`zip文件验证失败: ${zipValid.error}`);
    }

    // 10. 显示压缩统计信息
    const compressionStats = getCompressionStats(distDir, zipFilePath);
    if (compressionStats) {
      showCompressionStats(compressionStats);
    }

    console.log("\x1b[32m%s\x1b[0m", "\n🎉 构建流程全部完成！");

    return {
      success: true,
      zipFile: zipResult,
      compressionStats,
    };
  } catch (error) {
    showBuildErrorMessage(error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * 构建主函数
 */
async function build() {
  const result = await executeBuild();

  if (!result.success) {
    process.exit(1);
  }

  return result;
}

module.exports = {
  build,
  executeBuild,
};
