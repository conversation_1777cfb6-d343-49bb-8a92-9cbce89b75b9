const fs = require("fs");
const path = require("path");

/**
 * 递归删除目录中的所有文件和子目录
 * @param {string} folderPath - 要删除的文件夹路径
 * @param {string} preserveDir - 要保留的目录路径（不删除该目录本身）
 */
function deleteFolderRecursive(folderPath, preserveDir = null) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        // 递归删除子目录
        deleteFolderRecursive(curPath);
      } else {
        // 删除文件
        fs.unlinkSync(curPath);
      }
    });

    // 不删除指定的保留目录本身，只删除其内容
    if (preserveDir && folderPath !== preserveDir) {
      fs.rmdirSync(folderPath);
    } else if (!preserveDir) {
      fs.rmdirSync(folderPath);
    }
  }
}

/**
 * 清理构建目录
 * @param {string} distDir - 构建目录路径
 */
function cleanBuildDirectory(distDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在清理之前的构建文件...");
  
  // 清空dist目录，但保留目录本身
  deleteFolderRecursive(distDir, distDir);
  console.log("\x1b[32m%s\x1b[0m", "已清空dist目录");

  // 确保dist目录存在
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
}

/**
 * 清理指定的文件
 * @param {Array<string>} filePaths - 要删除的文件路径数组
 */
function cleanFiles(filePaths) {
  filePaths.forEach((filePath) => {
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`\x1b[32m%s\x1b[0m`, `已删除文件: ${path.basename(filePath)}`);
      } catch (error) {
        console.log(`\x1b[33m%s\x1b[0m`, `删除文件失败: ${path.basename(filePath)} - ${error.message}`);
      }
    }
  });
}

/**
 * 清理临时文件
 * @param {string} rootDir - 项目根目录
 */
function cleanTempFiles(rootDir) {
  const tempFiles = [
    path.join(rootDir, "*.zip"), // 清理之前的zip文件
  ];
  
  // 查找所有zip文件
  const files = fs.readdirSync(rootDir);
  const zipFiles = files
    .filter(file => file.endsWith('.zip'))
    .map(file => path.join(rootDir, file));
  
  if (zipFiles.length > 0) {
    console.log("\x1b[33m%s\x1b[0m", "正在清理之前的zip文件...");
    cleanFiles(zipFiles);
  }
}

module.exports = {
  deleteFolderRecursive,
  cleanBuildDirectory,
  cleanFiles,
  cleanTempFiles,
};
