const { execSync } = require("child_process");
// 获取应用程序名称
const packageJson = require("../package.json");
const appName = packageJson.name;

/**
 * 使用 pkg 打包应用程序
 * @param {Array<string>} targets - 目标平台数组
 * @param {string} rootDir - 项目根目录
 */
function packageApplication(targets, rootDir) {
  console.log("\x1b[33m%s\x1b[0m", "正在打包应用程序...");

  // 执行打包命令
  for (const target of targets) {
    console.log(`\x1b[33m%s\x1b[0m`, `正在构建 ${target} 版本...`);
    try {
      execSync(`npx pkg . --target ${target} --out-path dist`, {
        stdio: "inherit",
        cwd: rootDir,
      });
      console.log(`\x1b[32m%s\x1b[0m`, `${target} 版本构建完成`);
    } catch (error) {
      console.error(`\x1b[31m%s\x1b[0m`, `构建 ${target} 版本时出错:`);
      throw error;
    }
  }
}

/**
 * 验证打包结果
 * @param {string} distDir - 构建目录路径
 * @param {Array<string>} targets - 目标平台数组
 * @returns {boolean} 是否验证成功
 */
function validatePackageResult(distDir, targets) {
  const fs = require("fs");
  const path = require("path");

  console.log("\x1b[33m%s\x1b[0m", "正在验证打包结果...");

  let allValid = true;

  for (const target of targets) {
    let expectedFileName;
    if (target.includes("win")) {
      expectedFileName = `${appName}.exe`;
    } else if (target.includes("macos")) {
      expectedFileName = appName;
    } else if (target.includes("linux")) {
      expectedFileName = appName;
    }

    const expectedFilePath = path.join(distDir, expectedFileName);
    if (fs.existsSync(expectedFilePath)) {
      const stats = fs.statSync(expectedFilePath);
      const fileSizeInMB = (stats.size / 1024 / 1024).toFixed(2);
      console.log(
        `\x1b[32m%s\x1b[0m`,
        `✓ ${expectedFileName} (${fileSizeInMB} MB)`
      );
    } else {
      console.log(`\x1b[31m%s\x1b[0m`, `✗ ${expectedFileName} 未找到`);
      allValid = false;
    }
  }

  if (allValid) {
    console.log("\x1b[32m%s\x1b[0m", "所有目标平台打包成功");
  } else {
    console.log("\x1b[31m%s\x1b[0m", "部分目标平台打包失败");
  }

  return allValid;
}

/**
 * 获取可执行文件信息
 * @param {string} distDir - 构建目录路径
 * @returns {Object} 可执行文件信息
 */
function getExecutableInfo(distDir) {
  const fs = require("fs");
  const path = require("path");

  const exeFiles = [];
  const files = fs.readdirSync(distDir);

  files.forEach((file) => {
    const filePath = path.join(distDir, file);
    const stats = fs.statSync(filePath);

    if (
      stats.isFile() &&
      (file.endsWith(".exe") ||
        (!file.includes(".") && stats.mode & parseInt("111", 8)))
    ) {
      exeFiles.push({
        name: file,
        path: filePath,
        size: stats.size,
        sizeInMB: (stats.size / 1024 / 1024).toFixed(2),
      });
    }
  });

  return exeFiles;
}

module.exports = {
  packageApplication,
  validatePackageResult,
  getExecutableInfo,
};
