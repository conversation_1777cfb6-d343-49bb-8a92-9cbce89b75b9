const fs = require("fs");
const path = require("path");
const archiver = require("archiver");

/**
 * 创建zip文件
 * @param {string} distDir - 构建目录路径
 * @param {string} zipFilePath - zip文件路径
 * @param {Function} onComplete - 完成回调函数
 * @param {Function} onError - 错误回调函数
 * @returns {Promise} Promise对象
 */
function createZipFile(distDir, zipFilePath, onComplete, onError) {
  console.log("\x1b[33m%s\x1b[0m", "正在创建zip文件...");

  return new Promise((resolve, reject) => {
    // 创建一个文件流来写入zip文件
    const output = fs.createWriteStream(zipFilePath);
    const archive = archiver("zip", {
      zlib: { level: 9 }, // 设置压缩级别
    });

    // 监听所有存档数据都已被写入底层流的'close'事件
    output.on("close", function () {
      const fileSizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
      const zipFileName = path.basename(zipFilePath);
      
      if (onComplete) {
        onComplete(zipFileName, fileSizeInMB);
      }
      
      resolve({
        fileName: zipFileName,
        filePath: zipFilePath,
        sizeInMB: fileSizeInMB,
        sizeInBytes: archive.pointer()
      });
    });

    // 监听错误
    archive.on("error", function (err) {
      if (onError) {
        onError(err);
      }
      reject(err);
    });

    // 监听警告
    archive.on("warning", function (err) {
      if (err.code === "ENOENT") {
        console.log(`\x1b[33m%s\x1b[0m`, `警告: ${err.message}`);
      } else {
        console.log(`\x1b[31m%s\x1b[0m`, `归档警告: ${err.message}`);
      }
    });

    // 将输出流管道连接到归档
    archive.pipe(output);

    // 将整个目录添加到归档
    archive.directory(distDir, false);

    // 完成归档
    archive.finalize();
  });
}

/**
 * 验证zip文件
 * @param {string} zipFilePath - zip文件路径
 * @returns {Object} 验证结果
 */
function validateZipFile(zipFilePath) {
  console.log("\x1b[33m%s\x1b[0m", "正在验证zip文件...");
  
  try {
    if (!fs.existsSync(zipFilePath)) {
      console.log("\x1b[31m%s\x1b[0m", "✗ zip文件不存在");
      return { valid: false, error: "文件不存在" };
    }

    const stats = fs.statSync(zipFilePath);
    const fileSizeInMB = (stats.size / 1024 / 1024).toFixed(2);
    
    if (stats.size === 0) {
      console.log("\x1b[31m%s\x1b[0m", "✗ zip文件为空");
      return { valid: false, error: "文件为空" };
    }

    console.log(`\x1b[32m%s\x1b[0m`, `✓ zip文件验证通过 (${fileSizeInMB} MB)`);
    return { 
      valid: true, 
      size: stats.size,
      sizeInMB: fileSizeInMB,
      fileName: path.basename(zipFilePath)
    };
  } catch (err) {
    console.log(`\x1b[31m%s\x1b[0m`, `✗ zip文件验证失败: ${err.message}`);
    return { valid: false, error: err.message };
  }
}

/**
 * 获取压缩统计信息
 * @param {string} distDir - 构建目录路径
 * @param {string} zipFilePath - zip文件路径
 * @returns {Object} 压缩统计信息
 */
function getCompressionStats(distDir, zipFilePath) {
  try {
    // 计算原始目录大小
    function getDirectorySize(dirPath) {
      let totalSize = 0;
      
      function calculateSize(currentPath) {
        const stats = fs.statSync(currentPath);
        if (stats.isDirectory()) {
          const files = fs.readdirSync(currentPath);
          files.forEach(file => {
            calculateSize(path.join(currentPath, file));
          });
        } else {
          totalSize += stats.size;
        }
      }
      
      calculateSize(dirPath);
      return totalSize;
    }

    const originalSize = getDirectorySize(distDir);
    const compressedSize = fs.statSync(zipFilePath).size;
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

    return {
      originalSize,
      originalSizeInMB: (originalSize / 1024 / 1024).toFixed(2),
      compressedSize,
      compressedSizeInMB: (compressedSize / 1024 / 1024).toFixed(2),
      compressionRatio: `${compressionRatio}%`,
      compressionRatioNum: parseFloat(compressionRatio)
    };
  } catch (err) {
    console.log(`\x1b[33m%s\x1b[0m`, `获取压缩统计信息时出错: ${err.message}`);
    return null;
  }
}

/**
 * 显示压缩统计信息
 * @param {Object} stats - 压缩统计信息
 */
function showCompressionStats(stats) {
  if (!stats) return;
  
  console.log("\x1b[36m%s\x1b[0m", "压缩统计信息:");
  console.log(`  原始大小: ${stats.originalSizeInMB} MB`);
  console.log(`  压缩后大小: ${stats.compressedSizeInMB} MB`);
  console.log(`  压缩率: ${stats.compressionRatio}`);
}

/**
 * 清理临时文件
 * @param {Array<string>} tempFiles - 临时文件路径数组
 */
function cleanupTempFiles(tempFiles) {
  tempFiles.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`\x1b[32m%s\x1b[0m`, `已清理临时文件: ${path.basename(filePath)}`);
      }
    } catch (err) {
      console.log(`\x1b[33m%s\x1b[0m`, `清理临时文件失败: ${path.basename(filePath)} - ${err.message}`);
    }
  });
}

module.exports = {
  createZipFile,
  validateZipFile,
  getCompressionStats,
  showCompressionStats,
  cleanupTempFiles,
};
