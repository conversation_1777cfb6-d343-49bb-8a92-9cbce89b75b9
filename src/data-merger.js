const fs = require("fs");
const path = require("path");

/**
 * 读取项目文件夹中的所有JSON文件并合并数据
 * @param {string} projectFolder - 项目文件夹名称
 * @param {string} jsonBaseDir - JSON文件基础目录
 * @returns {Object} 合并后的数据结构
 */
function mergeProjectData(projectFolder, jsonBaseDir) {
  // 存储所有清单项的数据结构
  // 格式: { 分部工程编码|分部工程名称: { 清单编码|清单名称|特征: { 工程名称: 清单项 } } }
  const mergedData = {};

  const folderPath = path.join(jsonBaseDir, projectFolder);

  // 检查项目文件夹是否存在
  if (!fs.existsSync(folderPath) || !fs.statSync(folderPath).isDirectory()) {
    console.error(`错误：项目文件夹 ${projectFolder} 不存在或不是一个目录！`);
    return mergedData;
  }

  // 读取文件夹中的所有JSON文件
  const files = fs.readdirSync(folderPath);
  const jsonFiles = files.filter((file) => file.endsWith(".json"));

  console.log(
    `\x1b[36m[信息] \x1b[0m项目 \x1b[33m${projectFolder}\x1b[0m 中找到 \x1b[32m${jsonFiles.length}\x1b[0m 个JSON文件`
  );

  // 如果没有找到JSON文件，直接返回
  if (jsonFiles.length === 0) {
    console.log(
      `\x1b[31m[警告] \x1b[0m项目 \x1b[33m${projectFolder}\x1b[0m 中没有找到JSON文件，跳过处理`
    );
    return mergedData;
  }

  // 遍历每个JSON文件
  for (const jsonFile of jsonFiles) {
    const filePath = path.join(folderPath, jsonFile);
    const projectName = path.basename(jsonFile, ".json"); // 从文件名获取工程名称

    console.log(
      `\x1b[36m[处理] \x1b[0m文件: \x1b[33m${projectFolder}/${projectName}\x1b[0m`
    );

    // 读取JSON文件内容
    const jsonData = JSON.parse(fs.readFileSync(filePath, "utf8"));

    // 遍历JSON文件中的每个清单项
    for (const item of jsonData) {
      const divisionalWorkKey = `${item.divisionalWorkCode || ""}|${
        item.divisionalWorkName || "未分类"
      }`;
      const billKey = `${item.billName}|${item.feature || ""}`; // 使用清单名称+特征作为唯一标识

      // 初始化分部工程对象（如果不存在）
      if (!mergedData[divisionalWorkKey]) {
        mergedData[divisionalWorkKey] = {};
      }

      // 初始化清单对象（如果不存在）
      if (!mergedData[divisionalWorkKey][billKey]) {
        mergedData[divisionalWorkKey][billKey] = {};
      }

      // 添加清单项，按工程名称分组
      mergedData[divisionalWorkKey][billKey][projectName] = item;
    }
  }

  return mergedData;
}

/**
 * 计算第2级数据的汇总信息
 * @param {Object} projectItems - 项目清单项对象
 * @returns {Object} 汇总信息对象
 */
function calculateLevel2Summary(projectItems) {
  let totalQuantity = 0;
  let totalPrice = 0;
  let unit = "";
  let workContent = "";
  let maxUnitPrice = 0; // 记录第3级中最大的综合单价
  let minBillCode = ""; // 记录第3级中最小的清单编码

  // 获取工作内容和单位（假设所有项目的工作内容和单位相同）
  for (const item of Object.values(projectItems)) {
    workContent = item.workContent || "";
    unit = item.unit || "";
    break;
  }

  // 计算总数量和总价，并找出最大单价和最小清单编码
  for (const item of Object.values(projectItems)) {
    const itemQuantity = parseFloat(item.quantity) || 0;
    const itemUnitPrice = parseFloat(item.unitPrice) || 0;
    const itemTotalPrice = parseFloat(item.totalPrice) || 0;
    const itemBillCode = item.billCode || "";

    totalQuantity += itemQuantity;
    totalPrice += itemTotalPrice;

    // 更新最大单价
    if (itemUnitPrice > maxUnitPrice) {
      maxUnitPrice = itemUnitPrice;
    }

    // 更新最小清单编码
    if (itemBillCode && (!minBillCode || itemBillCode < minBillCode)) {
      minBillCode = itemBillCode;
    }
  }

  return {
    totalQuantity,
    totalPrice,
    unit,
    workContent,
    maxUnitPrice,
    minBillCode,
  };
}

/**
 * 准备第1级数据用于排序
 * @param {Object} mergedData - 合并后的数据
 * @returns {Array} 第1级数据数组
 */
function prepareLevel1Items(mergedData) {
  const level1Items = [];

  for (const [divisionalWorkKey, billItems] of Object.entries(mergedData)) {
    const [divisionalWorkCode, divisionalWorkName] =
      divisionalWorkKey.split("|");

    // 将第1级数据添加到数组中
    level1Items.push({
      divisionalWorkKey,
      divisionalWorkCode,
      divisionalWorkName,
      billItems,
    });
  }

  return level1Items;
}

/**
 * 准备第2级数据用于排序
 * @param {Object} billItems - 清单项对象
 * @returns {Array} 第2级数据数组
 */
function prepareLevel2Items(billItems) {
  const level2Items = [];

  for (const [billKey, projectItems] of Object.entries(billItems)) {
    const [billName, feature] = billKey.split("|");

    // 计算汇总信息
    const summary = calculateLevel2Summary(projectItems);

    // 将第2级数据添加到数组中
    level2Items.push({
      billKey,
      billName,
      feature,
      workContent: summary.workContent,
      unit: summary.unit,
      totalQuantity: summary.totalQuantity,
      maxUnitPrice: summary.maxUnitPrice,
      totalPrice: summary.totalPrice,
      minBillCode: summary.minBillCode,
      projectItems,
    });
  }

  return level2Items;
}

module.exports = {
  mergeProjectData,
  calculateLevel2Summary,
  prepareLevel1Items,
  prepareLevel2Items,
};
