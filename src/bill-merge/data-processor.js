/**
 * 处理项目名称，拆分为清单名称、项目特征和工作内容
 * @param {string} itemName - 原始项目名称
 * @returns {Object} 包含清单名称、项目特征和工作内容的对象
 */
function parseItemName(itemName) {
  let originalItemName = "";
  let cleanItemName = "";
  let itemFeature = "";
  let workContent = "";

  if (itemName) {
    const itemNameStr = itemName.toString();

    // 保存原始项目名称
    originalItemName = itemNameStr;

    // 提取清单名称（第一行）
    const lines = itemNameStr.split("\n");
    if (lines.length > 0) {
      cleanItemName = lines[0].trim();
    } else {
      cleanItemName = itemNameStr;
    }

    // 提取项目特征
    const featureMatch = itemNameStr.match(
      /\[项目特征\]([\s\S]*?)(?=\[工作内容\]|$)/
    );
    if (featureMatch && featureMatch[1]) {
      itemFeature = featureMatch[1].trim();
    }

    // 提取工作内容
    const contentMatch = itemNameStr.match(/\[工作内容\]([\s\S]*?)$/);
    if (contentMatch && contentMatch[1]) {
      workContent = contentMatch[1].trim();
    }
  }

  return {
    originalItemName,
    cleanItemName,
    itemFeature,
    workContent,
  };
}

/**
 * 创建清单项对象
 * @param {Object} rowData - 行数据
 * @param {Object} parsedName - 解析后的名称信息
 * @param {string} currentDivisionalWorkCode - 当前分部工程编码
 * @param {string} currentDivisionalWorkName - 当前分部工程名称
 * @returns {Object} 清单项对象
 */
function createBillItem(rowData, parsedName, currentDivisionalWorkCode, currentDivisionalWorkName) {
  const { serialNum, itemCode, unit, quantity, price, totalPrice } = rowData;
  const { originalItemName, cleanItemName, itemFeature, workContent } = parsedName;

  return {
    serialNo: serialNum ? serialNum.toString() : "",
    billCode: itemCode ? itemCode.toString() : "",
    name: originalItemName, // 原始项目名称
    billName: cleanItemName, // 清单名称（第一行）
    feature: itemFeature,
    workContent: workContent,
    unit: unit ? unit.toString() : "",
    quantity: typeof quantity === "number" ? quantity : 0,
    unitPrice: typeof price === "number" ? price : 0,
    totalPrice: typeof totalPrice === "number" ? totalPrice : 0,
    divisionalWorkCode: currentDivisionalWorkCode, // 分部工程编码
    divisionalWorkName: currentDivisionalWorkName, // 分部工程名称
  };
}

/**
 * 判断是否为分部工程行
 * @param {*} unit - 计量单位
 * @param {*} quantity - 工程数量
 * @returns {boolean} 是否为分部工程行
 */
function isDivisionalWorkRow(unit, quantity) {
  return !unit && !quantity;
}

/**
 * 判断是否为合计行
 * @param {*} itemName - 项目名称
 * @returns {boolean} 是否为合计行
 */
function isTotalRow(itemName) {
  return (
    typeof itemName === "string" &&
    (itemName.includes("合计") ||
      itemName.includes("小计") ||
      itemName.includes("总计"))
  );
}

/**
 * 判断是否为有效的清单项
 * @param {Object} billItem - 清单项对象
 * @returns {boolean} 是否为有效的清单项
 */
function isValidBillItem(billItem) {
  return billItem.billCode && billItem.billName;
}

/**
 * 处理工作表数据，提取清单项
 * @param {Object} worksheet - Excel工作表对象
 * @param {Object} columnMapping - 列映射对象
 * @returns {Array} 清单项数组
 */
function processWorksheetData(worksheet, columnMapping) {
  const billItems = [];
  let currentDivisionalWorkCode = "";
  let currentDivisionalWorkName = "";

  const headerRowNum = 4;
  const dataStartRow = headerRowNum + 1;

  console.log(
    `  \x1b[36m[信息] \x1b[0m数据起始行: \x1b[32m${dataStartRow}\x1b[0m`
  );

  // 遍历数据行
  for (let rowNum = dataStartRow; rowNum <= worksheet.rowCount; rowNum++) {
    const row = worksheet.getRow(rowNum);

    // 检查是否为空行
    let isEmpty = true;
    row.eachCell({ includeEmpty: false }, () => {
      isEmpty = false;
    });

    if (isEmpty) continue;

    // 使用列映射获取单元格的值
    const serialNum = row.getCell(columnMapping.serialNo).value;
    const itemCode = row.getCell(columnMapping.billCode).value;
    const itemName = row.getCell(columnMapping.itemName).value;
    const unit = row.getCell(columnMapping.unit).value;
    const quantity = row.getCell(columnMapping.quantity).value;
    const price = row.getCell(columnMapping.unitPrice).value;
    const totalPrice = row.getCell(columnMapping.totalPrice).value;

    // 输出调试信息
    if (serialNum && itemCode && quantity) {
      // 简化输出，只在每10个清单项时显示一次进度
      if (parseInt(serialNum) % 10 === 0) {
        console.log(
          `  \x1b[36m[进度] \x1b[0m已处理到第 \x1b[32m${serialNum}\x1b[0m 项清单`
        );
      }
    }

    // 跳过空行或没有项目名称的行
    if (!itemName) continue;

    // 跳过合计行
    if (isTotalRow(itemName)) {
      continue;
    }

    // 判断是否为分部工程行
    if (isDivisionalWorkRow(unit, quantity)) {
      // 更新当前的分部工程信息
      currentDivisionalWorkCode = itemCode ? itemCode.toString() : "";
      currentDivisionalWorkName = itemName ? itemName.toString() : "";
      console.log(
        `  \x1b[36m[发现] \x1b[0m分部工程: \x1b[33m${currentDivisionalWorkName}\x1b[0m ${
          currentDivisionalWorkCode
            ? `(编码: \x1b[32m${currentDivisionalWorkCode}\x1b[0m)`
            : ""
        }`
      );
      continue; // 跳过分部工程行，不将其作为清单项
    }

    // 处理项目名称
    const parsedName = parseItemName(itemName);

    // 创建清单项
    const billItem = createBillItem(
      { serialNum, itemCode, unit, quantity, price, totalPrice },
      parsedName,
      currentDivisionalWorkCode,
      currentDivisionalWorkName
    );

    // 如果是有效的清单项，才添加到数组中
    if (isValidBillItem(billItem)) {
      billItems.push(billItem);
    }
  }

  return billItems;
}

module.exports = {
  parseItemName,
  createBillItem,
  isDivisionalWorkRow,
  isTotalRow,
  isValidBillItem,
  processWorksheetData,
};
