/**
 * 自然排序函数，正确处理字符串中的数字
 * @param {string} a - 第一个字符串
 * @param {string} b - 第二个字符串
 * @returns {number} 排序结果
 */
function naturalSort(a, b) {
  // 将字符串分解为数字和非数字部分
  const aParts = a.match(/(\d+|\D+)/g) || [];
  const bParts = b.match(/(\d+|\D+)/g) || [];

  const maxLength = Math.max(aParts.length, bParts.length);

  for (let i = 0; i < maxLength; i++) {
    const aPart = aParts[i] || "";
    const bPart = bParts[i] || "";

    // 如果两个部分都是数字，按数字大小比较
    if (/^\d+$/.test(aPart) && /^\d+$/.test(bPart)) {
      const aNum = parseInt(aPart);
      const bNum = parseInt(bPart);
      if (aNum !== bNum) {
        return aNum - bNum;
      }
    } else {
      // 否则按字符串比较
      const comparison = aPart.localeCompare(bPart);
      if (comparison !== 0) {
        return comparison;
      }
    }
  }

  return 0;
}

/**
 * 对第1级数据（分部工程）进行排序
 * @param {Array} level1Items - 第1级数据数组
 * @returns {Array} 排序后的第1级数据数组
 */
function sortLevel1Items(level1Items) {
  return level1Items.sort((a, b) => {
    // 如果两个分部工程都有编码，按编码排序
    if (a.divisionalWorkCode && b.divisionalWorkCode) {
      return a.divisionalWorkCode.localeCompare(b.divisionalWorkCode);
    }
    // 如果只有一个有编码，有编码的排在前面
    if (a.divisionalWorkCode) return -1;
    if (b.divisionalWorkCode) return 1;
    // 如果都没有编码，按分部工程名称排序
    return a.divisionalWorkName.localeCompare(b.divisionalWorkName);
  });
}

/**
 * 对第2级数据（清单项）进行排序
 * @param {Array} level2Items - 第2级数据数组
 * @returns {Array} 排序后的第2级数据数组
 */
function sortLevel2Items(level2Items) {
  return level2Items.sort((a, b) => {
    // 先按清单名称排序，再按清单编码排序
    const nameComparison = a.billName.localeCompare(b.billName);
    if (nameComparison !== 0) return nameComparison;

    // 如果两个清单都有编码，按编码排序
    if (a.minBillCode && b.minBillCode) {
      // 使用自然排序，确保数字部分正确排序
      return naturalSort(a.minBillCode, b.minBillCode);
    }
    // 如果只有一个有编码，有编码的排在前面
    if (a.minBillCode) return -1;
    if (b.minBillCode) return 1;

    // 没有名称、也没有编码，顺序一致
    return 0;
  });
}

/**
 * 提取楼栋编号
 * @param {string} str - 项目名称字符串
 * @returns {number} 楼栋编号，如果没有则返回0
 */
function extractBuildingNumber(str) {
  // 匹配楼栋编号格式，如"东地块1#楼-地上土建工程"
  const match = str.match(/东地块(\d+)#/);
  return match ? parseInt(match[1]) : 0;
}

/**
 * 提取S+数字格式的编号
 * @param {string} str - 项目名称字符串
 * @returns {number} S编号，如果没有则返回0
 */
function extractSNumber(str) {
  const match = str.match(/S(\d+)/);
  return match ? parseInt(match[1]) : 0;
}

/**
 * 对第3级数据（工程项目）进行排序
 * @param {Array} projectEntries - 第3级数据数组
 * @returns {Array} 排序后的第3级数据数组
 */
function sortLevel3Items(projectEntries) {
  return projectEntries.sort((a, b) => {
    const nameA = a[0]; // 项目名称A
    const nameB = b[0]; // 项目名称B

    // 检查是否包含楼栋编号
    const buildingNumA = extractBuildingNumber(nameA);
    const buildingNumB = extractBuildingNumber(nameB);

    // 如果两者都有楼栋编号，按楼栋编号排序
    if (buildingNumA > 0 && buildingNumB > 0) {
      return buildingNumA - buildingNumB;
    }

    // 如果只有一个有楼栋编号，有楼栋编号的排在前面
    if (buildingNumA > 0) return -1;
    if (buildingNumB > 0) return 1;

    // 检查是否包含S+数字格式，如"东地块S1纯商业"
    const sNumA = extractSNumber(nameA);
    const sNumB = extractSNumber(nameB);

    // 如果两者都是S+数字格式，按数字部分排序
    if (sNumA > 0 && sNumB > 0) {
      return sNumA - sNumB;
    }

    // 如果只有一个是S+数字格式，S+数字格式排在后面
    if (sNumA > 0) return 1;
    if (sNumB > 0) return -1;

    // 其他情况按字符串排序
    return nameA.localeCompare(nameB);
  });
}

module.exports = {
  sortLevel1Items,
  sortLevel2Items,
  sortLevel3Items,
};
