const fs = require("fs");
const path = require("path");
const {
  inputDir,
  jsonBaseDir,
  outputDir,
  ensureDirectories,
  showWelcomeMessage,
  configureConsole,
} = require("./config");
const {
  getColumnMapping,
  applyDefaultColumnMapping,
  getProjectName,
  readExcelFile,
  getExcelFiles,
} = require("./excel-reader");
const { processWorksheetData } = require("./data-processor");
const { mergeJsonFiles } = require("./bill-merger");

/**
 * 处理单个工作表
 * @param {Object} worksheet - Excel工作表对象
 * @param {string} projectJsonDir - 项目JSON目录路径
 */
function processWorksheet(worksheet, projectJsonDir) {
  console.log(`\x1b[36m[处理] \x1b[0m工作表: \x1b[33m${worksheet.name}\x1b[0m`);

  // 获取工程名称
  const projectName = getProjectName(worksheet);

  if (!projectName) {
    console.log(
      `  \x1b[31m[警告] \x1b[0m在工作表 \x1b[33m${worksheet.name}\x1b[0m 中未找到工程名称，跳过此工作表`
    );
    return;
  }

  console.log(
    `  \x1b[36m[信息] \x1b[0m工程名称: \x1b[32m${projectName}\x1b[0m`
  );

  // 获取表头所在列的映射
  const headerRowNum = 4;
  const headerRow = worksheet.getRow(headerRowNum);
  const columnMapping = getColumnMapping(headerRow);

  // 应用默认列映射（如果需要）
  applyDefaultColumnMapping(columnMapping);

  // 处理工作表数据
  const billItems = processWorksheetData(worksheet, columnMapping);

  // 将清单数据保存为JSON文件
  const jsonFilePath = path.join(projectJsonDir, `${projectName}.json`);
  fs.writeFileSync(jsonFilePath, JSON.stringify(billItems, null, 2), "utf8");
  console.log(
    `  \x1b[32m[保存] \x1b[0m已保存清单数据到: \x1b[33m${jsonFilePath}\x1b[0m`
  );
}

/**
 * 处理单个Excel文件
 * @param {string} excelFile - Excel文件名
 */
async function processExcelFileData(excelFile) {
  const excelFilePath = path.join(inputDir, excelFile);
  const projectFolder = path.basename(excelFile, path.extname(excelFile));

  console.log(`\n\x1b[36m[处理] \x1b[0m文件: \x1b[33m${excelFile}\x1b[0m`);

  // 创建项目对应的JSON文件夹
  const projectJsonDir = path.join(jsonBaseDir, projectFolder);
  if (!fs.existsSync(projectJsonDir)) {
    fs.mkdirSync(projectJsonDir, { recursive: true });
  }

  // 读取Excel文件
  const workbook = await readExcelFile(excelFilePath);

  // 处理每个工作表
  workbook.eachSheet((worksheet) => {
    processWorksheet(worksheet, projectJsonDir);
  });

  return projectFolder;
}

/**
 * 等待用户输入后退出
 */
function waitForUserExit() {
  console.log("\x1b[33m按回车键退出...\x1b[0m");
  process.stdin.setRawMode(true);
  process.stdin.resume();
  process.stdin.on("data", process.exit.bind(process, 0));
}

/**
 * 显示完成信息
 */
function showCompletionMessage() {
  console.log("\n\x1b[36m[完成] \x1b[32m所有处理完成！\x1b[0m");
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log("\x1b[32m成功生成合并清单Excel文件，保存在output文件夹中\x1b[0m");
}

/**
 * 主函数
 */
async function processExcelFile() {
  // 配置控制台输出
  configureConsole();

  try {
    // 确保目录存在
    ensureDirectories();

    // 显示欢迎信息
    showWelcomeMessage();

    console.log("\n\x1b[36m[开始] \x1b[33m开始处理Excel文件...\x1b[0m");

    // 1. 读取input文件夹下的所有Excel文件
    const excelFiles = getExcelFiles(inputDir);

    // 检查是否有Excel文件
    if (excelFiles.length === 0) {
      console.error(`\x1b[31m[错误] \x1b[0minput文件夹中没有找到Excel文件！`);
      console.log(`\x1b[33m[提示] \x1b[0m请将Excel文件放在input文件夹下。`);
      console.log("\x1b[36m[退出] \x1b[0m按回车键退出...");
      waitForUserExit();
      return;
    }

    console.log(
      `\x1b[36m[信息] \x1b[0m找到 \x1b[32m${excelFiles.length}\x1b[0m 个Excel文件`
    );

    // 2. 处理每个Excel文件
    const projectFolders = [];
    for (const excelFile of excelFiles) {
      const projectFolder = await processExcelFileData(excelFile);
      projectFolders.push(projectFolder);
    }

    console.log("\n\x1b[36m[完成] \x1b[32m处理原始清单Excel完成！\x1b[0m");

    // 3. 处理每个项目文件夹，生成对应的输出文件
    for (const projectFolder of projectFolders) {
      await mergeJsonFiles(projectFolder, jsonBaseDir, outputDir);
    }

    // 显示完成信息
    showCompletionMessage();
    waitForUserExit();
  } catch (error) {
    console.error(
      `\n\x1b[31m[错误] \x1b[0m处理文件时出错: ${error.message}\x1b[0m`
    );
    waitForUserExit();
  }
}

module.exports = {
  processExcelFile,
};
