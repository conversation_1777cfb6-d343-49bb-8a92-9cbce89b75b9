const fs = require("fs");
const path = require("path");

// 获取应用程序的根目录
const isPackaged = process.pkg !== undefined;
const rootDir = isPackaged
  ? path.dirname(process.execPath)
  : path.dirname(path.dirname(__dirname));

// 获取应用程序版本号
const packageJson = require("../../package.json");
const appVersion = packageJson.version;

// 创建必要的目录结构
const inputDir = path.join(rootDir, "input");
const jsonBaseDir = path.join(rootDir, "json");
const outputDir = path.join(rootDir, "output");

// 确保目录存在
function ensureDirectories() {
  [inputDir, jsonBaseDir, outputDir].forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// 显示欢迎信息
function showWelcomeMessage() {
  console.log(
    "\n\x1b[36m==================================================\x1b[0m"
  );
  console.log(`\x1b[36m    欢迎使用清单合并工具 v${appVersion}\x1b[0m`);
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log(
    "\x1b[32m本工具将自动读取 input 文件夹中的清单文件，进行数据整理、合并、分组，\n最终将合并后的清单输出在 output 文件夹下。\x1b[0m"
  );
  console.log(
    "\x1b[33m使用方法：将清单Excel文件放在input文件夹下，然后运行本程序。\x1b[0m"
  );
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
}

// 配置控制台输出
function configureConsole() {
  console.log = function () {
    process.stdout.write(Array.from(arguments).join(" ") + "\n");
  };
  console.error = function () {
    process.stderr.write(Array.from(arguments).join(" ") + "\n");
  };
}

module.exports = {
  rootDir,
  appVersion,
  inputDir,
  jsonBaseDir,
  outputDir,
  ensureDirectories,
  showWelcomeMessage,
  configureConsole,
};
