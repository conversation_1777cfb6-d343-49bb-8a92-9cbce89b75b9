const path = require("path");
const {
  mergeProjectData,
  prepareLevel1Items,
  prepareLevel2Items,
} = require("./data-merger");
const {
  sortLevel1Items,
  sortLevel2Items,
  sortLevel3Items,
} = require("./sort-utils");
const {
  createWorkbook,
  addLevel1Row,
  addLevel2Row,
  addLevel3Row,
  setCellStyles,
} = require("./excel-writer");

/**
 * 合并JSON文件并生成合并后的Excel文件
 * @param {string} projectFolder - 项目文件夹名称
 * @param {string} jsonBaseDir - JSON文件基础目录
 * @param {string} outputDir - 输出目录
 */
async function mergeJsonFiles(projectFolder, jsonBaseDir, outputDir) {
  console.log(
    `\n\x1b[36m[处理] \x1b[33m开始处理项目 ${projectFolder} 的JSON文件...\x1b[0m`
  );

  try {
    // 合并项目数据
    const mergedData = mergeProjectData(projectFolder, jsonBaseDir);

    // 如果没有数据，直接返回
    if (Object.keys(mergedData).length === 0) {
      return;
    }

    // 创建Excel工作簿
    const { workbook, worksheet } = createWorkbook();

    // 将合并后的数据写入Excel
    let rowIndex = 2; // 从第2行开始写入数据（第1行是表头）
    let level1Count = 1; // 第1级序号计数器

    // 准备并排序第1级数据
    const level1Items = prepareLevel1Items(mergedData);
    const sortedLevel1Items = sortLevel1Items(level1Items);

    // 遍历排序后的第1级数据
    for (const level1Item of sortedLevel1Items) {
      const { divisionalWorkCode, divisionalWorkName, billItems } = level1Item;

      // 写入分部工程行（1级数据）
      const level1SerialNo = level1Count.toString();
      const { divisionalWorkRow, rowIndex: newRowIndex1 } = addLevel1Row(
        worksheet,
        { divisionalWorkCode, divisionalWorkName },
        level1SerialNo,
        rowIndex
      );
      rowIndex = newRowIndex1;

      // 初始化第2级序号计数器
      let level2Count = 1;

      // 准备并排序第2级数据
      const level2Items = prepareLevel2Items(billItems);
      const sortedLevel2Items = sortLevel2Items(level2Items);

      // 遍历排序后的第2级数据
      for (const level2Item of sortedLevel2Items) {
        const {
          billName,
          feature,
          workContent,
          unit,
          totalQuantity,
          maxUnitPrice,
          totalPrice,
          minBillCode,
          projectItems,
        } = level2Item;

        // 先生成分组（2级数据）
        const level2SerialNo = `${level1SerialNo}.${level2Count}`;
        const { billRow, rowIndex: newRowIndex2 } = addLevel2Row(
          worksheet,
          level2Item,
          level2SerialNo,
          rowIndex
        );
        rowIndex = newRowIndex2;

        // 初始化第3级序号计数器
        let level3Count = 1;

        // 对3级数据进行排序
        const projectEntries = Object.entries(projectItems);
        const sortedProjectEntries = sortLevel3Items(projectEntries);

        // 遍历排序后的每个工程下的清单项（3级数据）
        for (const [projectName, item] of sortedProjectEntries) {
          // 添加第3级数据行
          const level3SerialNo = `${level2SerialNo}.${level3Count}`;
          rowIndex = addLevel3Row(
            worksheet,
            projectName,
            item,
            level3SerialNo,
            unit,
            minBillCode,
            maxUnitPrice,
            rowIndex
          );

          // 递增第3级序号计数器
          level3Count++;
        }

        // 递增第2级序号计数器
        level2Count++;

        // 设置分组级别（用于展开/收起）
        billRow.outlineLevel = 1; // 第2级数据的大纲级别
      }

      // 递增第1级序号计数器
      level1Count++;

      // 设置分组级别（用于展开/收起）
      divisionalWorkRow.outlineLevel = 0; // 第1级数据的大纲级别
    }

    // 设置单元格样式
    setCellStyles(worksheet, rowIndex - 1);

    // 保存Excel文件，使用与输入文件相同的名称
    const outputExcelPath = path.join(outputDir, `${projectFolder}.xlsx`);
    await workbook.xlsx.writeFile(outputExcelPath);
    console.log(
      `\x1b[32m[成功] \x1b[0m合并清单Excel文件已保存到: \x1b[33m${outputExcelPath}\x1b[0m`
    );
  } catch (error) {
    console.error(
      `\x1b[31m[错误] \x1b[0m合并JSON文件时出错: ${error.message}\x1b[0m`
    );
  }
}

module.exports = {
  mergeJsonFiles,
};
