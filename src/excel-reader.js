const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");

/**
 * 获取表头所在列的映射
 * @param {Object} headerRow - Excel表头行对象
 * @returns {Object} 列映射对象
 */
function getColumnMapping(headerRow) {
  const columnMapping = {
    serialNo: -1, // 序号
    billCode: -1, // 清单编码
    itemName: -1, // 项目名称
    unit: -1, // 计量单位
    quantity: -1, // 工程数量
    unitPrice: -1, // 综合单价
    totalPrice: -1, // 合价
  };

  // 遍历表头行，查找各字段所在的列
  headerRow.eachCell({ includeEmpty: false }, (cell, colNumber) => {
    const headerText = cell.value ? cell.value.toString().trim() : "";

    if (headerText.includes("序号")) {
      columnMapping.serialNo = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到序号列: \x1b[32m${colNumber}\x1b[0m`
      );
    } else if (
      headerText.includes("清单编码") ||
      headerText.includes("项目编码")
    ) {
      columnMapping.billCode = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到清单编码列: \x1b[32m${colNumber}\x1b[0m`
      );
    } else if (
      headerText.includes("项目名称") ||
      headerText.includes("清单名称")
    ) {
      columnMapping.itemName = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到项目名称列: \x1b[32m${colNumber}\x1b[0m`
      );
    } else if (
      headerText.includes("单位") ||
      headerText.includes("计量单位")
    ) {
      columnMapping.unit = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到计量单位列: \x1b[32m${colNumber}\x1b[0m`
      );
    } else if (
      headerText.includes("工程数量") ||
      headerText.includes("数量")
    ) {
      columnMapping.quantity = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到工程数量列: \x1b[32m${colNumber}\x1b[0m`
      );
    } else if (
      headerText.includes("综合单价") ||
      headerText.includes("单价")
    ) {
      columnMapping.unitPrice = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到综合单价列: \x1b[32m${colNumber}\x1b[0m`
      );
    } else if (
      headerText.includes("合价") ||
      headerText.includes("金额")
    ) {
      columnMapping.totalPrice = colNumber;
      console.log(
        `  \x1b[36m[映射] \x1b[0m找到合价列: \x1b[32m${colNumber}\x1b[0m`
      );
    }
  });

  return columnMapping;
}

/**
 * 使用默认列映射
 * @param {Object} columnMapping - 列映射对象
 */
function applyDefaultColumnMapping(columnMapping) {
  const missingColumns = Object.entries(columnMapping)
    .filter(([_, value]) => value === -1)
    .map(([fieldName]) => fieldName);

  if (missingColumns.length > 0) {
    console.log(
      `  \x1b[31m[警告] \x1b[0m未找到以下列: \x1b[33m${missingColumns.join(
        ", "
      )}\x1b[0m`
    );
    console.log(`  \x1b[33m[提示] \x1b[0m将使用默认列映射`);

    // 使用默认列映射
    if (columnMapping.serialNo === -1) columnMapping.serialNo = 1; // A列
    if (columnMapping.billCode === -1) columnMapping.billCode = 2; // B列
    if (columnMapping.itemName === -1) columnMapping.itemName = 3; // C列
    if (columnMapping.unit === -1) columnMapping.unit = 6; // F列
    if (columnMapping.quantity === -1) columnMapping.quantity = 7; // G列
    if (columnMapping.unitPrice === -1) columnMapping.unitPrice = 9; // I列
    if (columnMapping.totalPrice === -1) columnMapping.totalPrice = 10; // J列
  }
}

/**
 * 获取工程名称
 * @param {Object} worksheet - Excel工作表对象
 * @returns {string} 工程名称
 */
function getProjectName(worksheet) {
  const projectNameCell = worksheet.getCell(2, 1);
  const projectName = projectNameCell.value
    ? projectNameCell.value
        .toString()
        .trim()
        .replace(/^工程名称：/, "")
    : "";
  
  return projectName;
}

/**
 * 读取Excel文件并返回工作簿对象
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} Excel工作簿对象
 */
async function readExcelFile(filePath) {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.readFile(filePath);
  return workbook;
}

/**
 * 获取input目录下的所有Excel文件
 * @param {string} inputDir - input目录路径
 * @returns {Array<string>} Excel文件名数组
 */
function getExcelFiles(inputDir) {
  return fs
    .readdirSync(inputDir)
    .filter((file) => file.endsWith(".xlsx") || file.endsWith(".xls"));
}

module.exports = {
  getColumnMapping,
  applyDefaultColumnMapping,
  getProjectName,
  readExcelFile,
  getExcelFiles,
};
