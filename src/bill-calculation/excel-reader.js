const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");

/**
 * 清单测算专用Excel读取器
 * 用于读取和解析"3-分部分项成本测算"工作表
 */

/**
 * 获取清单测算表头列映射
 * @param {Object} headerRow1 - 第一行表头
 * @param {Object} headerRow2 - 第二行表头
 * @returns {Object} 列映射对象
 */
function getCostCalculationColumnMapping(headerRow1, headerRow2) {
  const columnMapping = {
    costType: -1, // 成本目标+成本组成（第1列）
    costComponent: -1, // 成本目标+成本组成（第2列）
    feature: -1, // 项目特征
    unit: -1, // 计量单位
    totalQuantity: -1, // 合并后清单工程量
    content: -1, // 含量
    calculatedQuantity: -1, // 测算成本量
    unitPrice: -1, // 测算单价（含税）
    totalCost: -1, // 测算成本合计
    laborCost: -1, // 劳务费
    materialCost: -1, // 材料费
    subcontractCost: -1, // 专业分包
    comprehensiveUnitPrice: -1, // 测算综合单价
    taxRate: -1, // 税率
  };

  // 分析第一行表头
  headerRow1.eachCell({ includeEmpty: false }, (cell, colNumber) => {
    const headerText = cell.value ? cell.value.toString().trim() : "";

    if (headerText.includes("成本目标+成本组成")) {
      if (columnMapping.costType === -1) {
        columnMapping.costType = colNumber;
      } else if (columnMapping.costComponent === -1) {
        columnMapping.costComponent = colNumber;
      }
    } else if (headerText.includes("项目特征")) {
      columnMapping.feature = colNumber;
    } else if (headerText.includes("计量") && headerText.includes("单位")) {
      columnMapping.unit = colNumber;
    } else if (
      headerText.includes("合并后") &&
      headerText.includes("清单工程量")
    ) {
      columnMapping.totalQuantity = colNumber;
    } else if (headerText.includes("含量")) {
      columnMapping.content = colNumber;
    } else if (headerText.includes("测算成本量")) {
      columnMapping.calculatedQuantity = colNumber;
    } else if (headerText.includes("测算单价") && headerText.includes("含税")) {
      columnMapping.unitPrice = colNumber;
    } else if (headerText.includes("测算") && headerText.includes("综合单价")) {
      columnMapping.comprehensiveUnitPrice = colNumber;
    } else if (headerText.includes("税率")) {
      columnMapping.taxRate = colNumber;
    }
  });

  // 分析第二行表头（更具体的分类）
  headerRow2.eachCell({ includeEmpty: false }, (cell, colNumber) => {
    const headerText = cell.value ? cell.value.toString().trim() : "";

    if (headerText.includes("合计")) {
      columnMapping.totalCost = colNumber;
    } else if (headerText.includes("劳务费")) {
      columnMapping.laborCost = colNumber;
    } else if (headerText.includes("材料费")) {
      columnMapping.materialCost = colNumber;
    } else if (headerText.includes("专业分包")) {
      columnMapping.subcontractCost = colNumber;
    }
  });

  return columnMapping;
}

/**
 * 应用默认列映射（基于需求文档分析的标准格式）
 * @param {Object} columnMapping - 列映射对象
 */
function applyDefaultCostCalculationMapping(columnMapping) {
  const missingColumns = Object.entries(columnMapping)
    .filter(([_, value]) => value === -1)
    .map(([fieldName]) => fieldName);

  if (missingColumns.length > 0) {
    console.log(
      `  \x1b[31m[警告] \x1b[0m未找到以下列: \x1b[33m${missingColumns.join(
        ", "
      )}\x1b[0m`
    );
    console.log(`  \x1b[33m[提示] \x1b[0m将使用默认列映射`);

    // 基于需求文档的默认列映射
    if (columnMapping.costType === -1) columnMapping.costType = 1; // A列
    if (columnMapping.costComponent === -1) columnMapping.costComponent = 2; // B列
    if (columnMapping.feature === -1) columnMapping.feature = 3; // C列
    if (columnMapping.unit === -1) columnMapping.unit = 4; // D列
    if (columnMapping.totalQuantity === -1) columnMapping.totalQuantity = 5; // E列
    if (columnMapping.content === -1) columnMapping.content = 6; // F列
    if (columnMapping.calculatedQuantity === -1)
      columnMapping.calculatedQuantity = 7; // G列
    if (columnMapping.unitPrice === -1) columnMapping.unitPrice = 8; // H列
    if (columnMapping.totalCost === -1) columnMapping.totalCost = 9; // I列
    if (columnMapping.laborCost === -1) columnMapping.laborCost = 10; // J列
    if (columnMapping.materialCost === -1) columnMapping.materialCost = 11; // K列
    if (columnMapping.subcontractCost === -1)
      columnMapping.subcontractCost = 12; // L列
    if (columnMapping.comprehensiveUnitPrice === -1)
      columnMapping.comprehensiveUnitPrice = 13; // M列
    if (columnMapping.taxRate === -1) columnMapping.taxRate = 14; // N列
  }
}

/**
 * 读取Excel文件并返回工作簿对象
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} Excel工作簿对象
 */
async function readCostCalculationExcel(filePath) {
  try {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    return workbook;
  } catch (error) {
    throw new Error(`读取Excel文件失败: ${error.message}`);
  }
}

/**
 * 获取input/bill-calculation目录下的所有Excel文件
 * @param {string} inputDir - input目录路径
 * @returns {Array<string>} Excel文件名数组
 */
function getCostCalculationExcelFiles(inputDir) {
  try {
    return fs
      .readdirSync(inputDir)
      .filter((file) => file.endsWith(".xlsx") || file.endsWith(".xls"))
      .filter((file) => !file.startsWith("~")); // 排除临时文件
  } catch (error) {
    console.error(`读取目录失败: ${error.message}`);
    return [];
  }
}

/**
 * 解析成本组成类型
 * @param {*} costTypeValue - 成本组成单元格值
 * @returns {string} 标准化的成本类型
 */
function parseCostType(costTypeValue) {
  if (!costTypeValue) return "";

  const costTypeStr = costTypeValue.toString().trim();

  // 标准化成本类型
  if (costTypeStr.includes("目标")) return "目标";
  if (costTypeStr.includes("人")) return "人";
  if (costTypeStr.includes("材")) return "材";
  if (costTypeStr.includes("专")) return "专";

  return costTypeStr;
}

/**
 * 解析项目名称（从项目特征列提取）
 * @param {*} featureValue - 项目特征单元格值
 * @returns {string} 项目名称
 */
function parseItemName(featureValue) {
  if (!featureValue) return "";

  // 处理复杂对象类型
  let featureStr = "";
  if (typeof featureValue === "object" && featureValue.richText) {
    // 处理富文本格式
    featureStr = featureValue.richText.map((rt) => rt.text || "").join("");
  } else if (typeof featureValue === "object" && featureValue.result) {
    // 处理公式结果
    featureStr = featureValue.result.toString();
  } else {
    featureStr = featureValue.toString();
  }

  featureStr = featureStr.trim();

  // 提取项目名称（通常在项目特征的第一行）
  const lines = featureStr.split("\n");
  return lines[0] ? lines[0].trim() : featureStr;
}

/**
 * 安全转换字符串
 * @param {*} value - 原始值
 * @returns {string} 转换后的字符串
 */
function safeParseString(value) {
  if (!value) return "";

  // 处理复杂对象类型
  if (typeof value === "object" && value.richText) {
    // 处理富文本格式
    return value.richText
      .map((rt) => rt.text || "")
      .join("")
      .trim();
  } else if (typeof value === "object" && value.result) {
    // 处理公式结果
    return value.result.toString().trim();
  } else {
    return value.toString().trim();
  }
}

/**
 * 安全转换数值
 * @param {*} value - 原始值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的数值
 */
function safeParseNumber(value, defaultValue = 0) {
  if (value === null || value === undefined || value === "") {
    return defaultValue;
  }

  const num = parseFloat(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 创建成本项对象
 * @param {Object} rowData - 行数据
 * @param {number} rowIndex - 行索引
 * @param {string} currentDivision - 当前分部工程
 * @returns {Object} 成本项对象
 */
function createCostItem(rowData, rowIndex, currentDivision) {
  const {
    costType,
    costComponent,
    feature,
    unit,
    totalQuantity,
    content,
    calculatedQuantity,
    unitPrice,
    totalCost,
    laborCost,
    materialCost,
    subcontractCost,
    comprehensiveUnitPrice,
    taxRate,
  } = rowData;

  // 生成唯一ID
  const id = `cost_${rowIndex}_${Date.now()}`;

  // 解析成本类型
  const parsedCostType = parseCostType(costType);

  // 解析项目名称
  const itemName = parseItemName(feature);

  return {
    id,
    rowIndex,
    divisionalWork: currentDivision,
    costType: parsedCostType,
    itemName,
    feature: safeParseString(feature),
    unit: safeParseString(unit),
    totalQuantity: safeParseNumber(totalQuantity),
    content: safeParseNumber(content),
    calculatedQuantity: safeParseNumber(calculatedQuantity),
    unitPrice: safeParseNumber(unitPrice),
    totalCost: safeParseNumber(totalCost),
    laborCost: safeParseNumber(laborCost),
    materialCost: safeParseNumber(materialCost),
    subcontractCost: safeParseNumber(subcontractCost),
    comprehensiveUnitPrice: safeParseNumber(comprehensiveUnitPrice),
    taxRate: safeParseNumber(taxRate),
    remarks: "",
  };
}

/**
 * 判断是否为分部工程行
 * @param {Object} costItem - 成本项对象
 * @returns {boolean} 是否为分部工程行
 */
function isDivisionRow(costItem) {
  // 分部工程行通常没有单位和数量，但有项目名称
  return (
    costItem.itemName &&
    !costItem.unit &&
    costItem.totalQuantity === 0 &&
    costItem.content === 0
  );
}

/**
 * 判断是否为有效的成本项
 * @param {Object} costItem - 成本项对象
 * @returns {boolean} 是否为有效成本项
 */
function isValidCostItem(costItem) {
  // 必须有成本类型和项目名称
  return costItem.costType && costItem.itemName && costItem.costType !== "目标"; // 目标项单独处理
}

module.exports = {
  getCostCalculationColumnMapping,
  applyDefaultCostCalculationMapping,
  readCostCalculationExcel,
  getCostCalculationExcelFiles,
  parseCostType,
  parseItemName,
  safeParseString,
  safeParseNumber,
  createCostItem,
  isDivisionRow,
  isValidCostItem,
};
