const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");
const { UserInputData } = require("./types");
const { safeParseString, safeParseNumber, validateData } = require("./utils");
const { BusinessRules } = require("./config");

/**
 * Excel读取解析器
 * 专注于读取A~F列用户输入数据
 */

/**
 * 读取Excel文件
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} Excel工作簿对象
 */
async function readExcelFile(filePath) {
  try {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    return workbook;
  } catch (error) {
    throw new Error(`读取Excel文件失败: ${error.message}`);
  }
}

/**
 * 查找目标工作表（3-分部分项成本测算）
 * @param {Object} workbook - Excel工作簿对象
 * @returns {Object|null} 目标工作表对象
 */
function findTargetWorksheet(workbook) {
  let targetWorksheet = null;
  
  workbook.eachSheet((worksheet) => {
    const sheetName = worksheet.name;
    if (sheetName.includes("3-分部分项") && 
        sheetName.includes("成本测算") && 
        !sheetName.includes("规则")) {
      targetWorksheet = worksheet;
      console.log(`  \x1b[36m[发现] \x1b[0m找到目标工作表: \x1b[32m${worksheet.name}\x1b[0m`);
      return;
    }
  });
  
  // 如果没找到，尝试查找包含"3-"但不包含"规则"的工作表
  if (!targetWorksheet) {
    workbook.eachSheet((worksheet) => {
      const sheetName = worksheet.name;
      if ((sheetName.includes("3-") || sheetName.includes("3 ")) && 
          !sheetName.includes("规则")) {
        targetWorksheet = worksheet;
        console.log(`  \x1b[36m[发现] \x1b[0m找到可能的目标工作表: \x1b[32m${worksheet.name}\x1b[0m`);
        return;
      }
    });
  }
  
  return targetWorksheet;
}

/**
 * 分析工作表结构，确定数据起始行
 * @param {Object} worksheet - Excel工作表对象
 * @returns {Object} 表结构信息
 */
function analyzeWorksheetStructure(worksheet) {
  let headerRowIndex = -1;
  let dataStartRow = -1;
  
  // 查找表头行（通常包含"成本类型"、"项目名称"等关键字）
  for (let i = 1; i <= Math.min(10, worksheet.rowCount); i++) {
    const row = worksheet.getRow(i);
    let rowText = "";
    
    row.eachCell({ includeEmpty: false }, (cell) => {
      rowText += cell.value ? cell.value.toString() : "";
    });
    
    if (rowText.includes("成本类型") || rowText.includes("项目名称") || rowText.includes("计量单位")) {
      headerRowIndex = i;
      console.log(`  \x1b[36m[分析] \x1b[0m表头位于第 \x1b[32m${i}\x1b[0m 行`);
      break;
    }
  }
  
  // 如果没找到表头，使用默认值
  if (headerRowIndex === -1) {
    headerRowIndex = 2; // 通常第2行是表头
    console.log(`  \x1b[33m[警告] \x1b[0m未找到表头，使用默认位置第2行`);
  }
  
  // 数据开始行通常在表头行之后
  dataStartRow = headerRowIndex + 1;
  
  return {
    headerRowIndex,
    dataStartRow,
  };
}

/**
 * 获取列映射（A~F列固定映射）
 * @returns {Object} 列映射对象
 */
function getColumnMapping() {
  return {
    division: 1,        // A列：分部工程
    costType: 2,        // B列：成本类型
    itemName: 3,        // C列：项目名称
    feature: 4,         // D列：项目特征
    unit: 5,            // E列：计量单位
    quantity: 6,        // F列：合并后清单工程量
    content: 7          // G列：含量
  };
}

/**
 * 从行中提取用户输入数据
 * @param {Object} row - Excel行对象
 * @param {number} rowIndex - 行索引
 * @param {Object} columnMapping - 列映射对象
 * @returns {UserInputData|null} 用户输入数据对象
 */
function extractUserInputData(row, rowIndex, columnMapping) {
  // 提取原始数据
  const rawData = {
    division: row.getCell(columnMapping.division).value,
    costType: row.getCell(columnMapping.costType).value,
    itemName: row.getCell(columnMapping.itemName).value,
    feature: row.getCell(columnMapping.feature).value,
    unit: row.getCell(columnMapping.unit).value,
    quantity: row.getCell(columnMapping.quantity).value,
    content: row.getCell(columnMapping.content).value
  };
  
  // 检查是否为空行
  if (!rawData.costType && !rawData.itemName) {
    return null;
  }
  
  // 转换数据类型
  const userData = new UserInputData({
    rowIndex,
    division: safeParseString(rawData.division),
    costType: safeParseString(rawData.costType),
    itemName: safeParseString(rawData.itemName),
    feature: safeParseString(rawData.feature),
    unit: safeParseString(rawData.unit),
    quantity: safeParseNumber(rawData.quantity),
    content: safeParseNumber(rawData.content)
  });
  
  return userData;
}

/**
 * 处理工作表数据，提取所有用户输入项
 * @param {Object} worksheet - Excel工作表对象
 * @returns {Array} 用户输入数据数组
 */
function processWorksheetData(worksheet) {
  console.log(`\n\x1b[36m[处理] \x1b[0m开始处理工作表: \x1b[32m${worksheet.name}\x1b[0m`);
  
  // 分析工作表结构
  const structure = analyzeWorksheetStructure(worksheet);
  const { dataStartRow } = structure;
  
  // 获取列映射
  const columnMapping = getColumnMapping();
  
  console.log(`  \x1b[36m[信息] \x1b[0m数据起始行: \x1b[32m${dataStartRow}\x1b[0m`);
  
  const userInputData = [];
  let currentDivision = "";
  let processedCount = 0;
  let validCount = 0;
  
  // 遍历数据行
  for (let rowNum = dataStartRow; rowNum <= worksheet.rowCount; rowNum++) {
    const row = worksheet.getRow(rowNum);
    
    // 检查是否为空行
    let isEmpty = true;
    row.eachCell({ includeEmpty: false }, () => {
      isEmpty = false;
    });
    
    if (isEmpty) continue;
    
    // 提取用户输入数据
    const userData = extractUserInputData(row, rowNum, columnMapping);
    
    if (!userData) continue;
    
    processedCount++;
    
    // 更新当前分部工程
    if (userData.division) {
      currentDivision = userData.division;
    } else if (currentDivision) {
      userData.division = currentDivision;
    }
    
    // 验证数据
    const validation = validateData(userData, BusinessRules.validation.required);
    if (!validation.isValid) {
      console.log(`  \x1b[33m[警告] \x1b[0m第${rowNum}行数据验证失败: ${validation.errors.join(", ")}`);
      continue;
    }
    
    // 添加有效数据
    if (userData.isValid()) {
      userInputData.push(userData);
      validCount++;
      
      // 每处理50个项目显示一次进度
      if (validCount % 50 === 0) {
        console.log(`  \x1b[36m[进度] \x1b[0m已处理 \x1b[32m${validCount}\x1b[0m 个有效项目`);
      }
    }
  }
  
  console.log(`  \x1b[36m[完成] \x1b[0m共处理 \x1b[32m${processedCount}\x1b[0m 行，有效数据 \x1b[32m${validCount}\x1b[0m 个`);
  return userInputData;
}

/**
 * 统计数据信息
 * @param {Array} userInputData - 用户输入数据数组
 * @returns {Object} 统计信息
 */
function generateStatistics(userInputData) {
  const stats = {
    totalItems: userInputData.length,
    targetItems: 0,
    materialItems: 0,
    laborItems: 0,
    subcontractItems: 0,
    divisions: new Set(),
    costTypes: new Set()
  };
  
  userInputData.forEach(item => {
    stats.divisions.add(item.division);
    stats.costTypes.add(item.costType);
    
    switch (item.costType) {
      case "目标":
        stats.targetItems++;
        break;
      case "材":
        stats.materialItems++;
        break;
      case "人":
        stats.laborItems++;
        break;
      case "专":
        stats.subcontractItems++;
        break;
    }
  });
  
  // 转换Set为Array
  stats.divisions = Array.from(stats.divisions);
  stats.costTypes = Array.from(stats.costTypes);
  
  return stats;
}

/**
 * 解析Excel文件主函数
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} 解析结果
 */
async function parseExcelFile(filePath) {
  console.log(`\n\x1b[36m[开始] \x1b[0m解析Excel文件: \x1b[32m${path.basename(filePath)}\x1b[0m`);
  
  try {
    // 读取Excel文件
    const workbook = await readExcelFile(filePath);
    console.log(`  \x1b[36m[成功] \x1b[0m文件读取完成，共 \x1b[32m${workbook.worksheets.length}\x1b[0m 个工作表`);
    
    // 查找目标工作表
    const targetWorksheet = findTargetWorksheet(workbook);
    if (!targetWorksheet) {
      throw new Error("未找到包含'3-分部分项成本测算'的工作表");
    }
    
    // 处理工作表数据
    const userInputData = processWorksheetData(targetWorksheet);
    
    // 生成统计信息
    const statistics = generateStatistics(userInputData);
    
    // 显示统计信息
    console.log(`  \x1b[36m[统计] \x1b[0m目标项: \x1b[32m${statistics.targetItems}\x1b[0m, 材料项: \x1b[32m${statistics.materialItems}\x1b[0m, 劳务项: \x1b[32m${statistics.laborItems}\x1b[0m, 专业分包项: \x1b[32m${statistics.subcontractItems}\x1b[0m`);
    console.log(`  \x1b[36m[统计] \x1b[0m分部工程: \x1b[32m${statistics.divisions.length}\x1b[0m 个`);
    
    return {
      success: true,
      fileName: path.basename(filePath),
      filePath,
      userInputData,
      statistics,
      worksheet: targetWorksheet
    };
    
  } catch (error) {
    console.error(`  \x1b[31m[错误] \x1b[0m解析文件失败: ${error.message}`);
    return {
      success: false,
      fileName: path.basename(filePath),
      filePath,
      error: error.message,
      userInputData: []
    };
  }
}

/**
 * 获取输入目录中的所有Excel文件
 * @param {string} inputDir - 输入目录路径
 * @returns {Array<string>} Excel文件路径数组
 */
function getExcelFiles(inputDir) {
  try {
    return fs
      .readdirSync(inputDir)
      .filter((file) => file.endsWith(".xlsx") || file.endsWith(".xls"))
      .filter((file) => !file.startsWith("~")) // 排除临时文件
      .map((file) => path.join(inputDir, file));
  } catch (error) {
    console.error(`读取目录失败: ${error.message}`);
    return [];
  }
}

module.exports = {
  readExcelFile,
  findTargetWorksheet,
  analyzeWorksheetStructure,
  getColumnMapping,
  extractUserInputData,
  processWorksheetData,
  generateStatistics,
  parseExcelFile,
  getExcelFiles
};
