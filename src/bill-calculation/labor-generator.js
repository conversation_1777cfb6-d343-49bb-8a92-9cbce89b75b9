const { LaborListItem, CostType } = require("./types");
const { BusinessRules } = require("./config");
const { 
  groupByDivision,
  groupByNameAndUnit,
  generateId
} = require("./utils");

/**
 * 劳务清单生成器
 * 实现劳务清单的分部合并、名称合并和层级结构
 */

/**
 * 创建分部合计行
 * @param {string} division - 分部工程名称
 * @param {number} serialNo - 序号
 * @param {Array} divisionItems - 分部下的所有项目
 * @returns {LaborListItem} 分部合计行
 */
function createDivisionTotalRow(division, serialNo, divisionItems) {
  const totalRow = new LaborListItem({
    serialNo: serialNo.toString(),
    division: division,
    originalItem: division,
    workContent: "",
    unit: "",
    content: 0,
    quantity: 0,
    unitPrice: 0,
    amount: 0,
    taxRate: 0,
    type: "division",
    level: 1
  });
  
  // 设置合计公式
  if (divisionItems.length > 0) {
    const startRow = divisionItems[0].rowIndex + 1; // Excel行号从1开始
    const endRow = divisionItems[divisionItems.length - 1].rowIndex + 1;
    totalRow.setFormula('amount', `=SUM(I${startRow}:I${endRow})`);
  }
  
  return totalRow;
}

/**
 * 创建名称合并行
 * @param {string} itemName - 项目名称
 * @param {string} unit - 计量单位
 * @param {Array} sameNameItems - 相同名称的项目数组
 * @returns {LaborListItem} 名称合并行
 */
function createNameMergedRow(itemName, unit, sameNameItems) {
  const mergedRow = new LaborListItem({
    division: sameNameItems[0].division,
    originalItem: itemName,
    workContent: sameNameItems[0].feature || "",
    unit: unit,
    content: 0,
    quantity: 0,
    unitPrice: 0,
    amount: 0,
    taxRate: sameNameItems[0].taxRate || 0.03,
    type: "merged",
    level: 2
  });
  
  // 计算合并数量和金额
  let totalQuantity = 0;
  let totalAmount = 0;
  
  sameNameItems.forEach(item => {
    totalQuantity += item.calculatedQuantity || 0;
    totalAmount += item.totalCost || 0;
  });
  
  mergedRow.quantity = totalQuantity;
  mergedRow.amount = totalAmount;
  
  // 设置Excel公式
  if (sameNameItems.length > 1) {
    const childRowNumbers = sameNameItems.map(item => item.rowIndex + 1);
    const quantityFormula = childRowNumbers.map(row => `G${row}`).join('+');
    const amountFormula = childRowNumbers.map(row => `I${row}`).join('+');
    
    mergedRow.setFormula('quantity', `=${quantityFormula}`);
    mergedRow.setFormula('amount', `=${amountFormula}`);
  }
  
  return mergedRow;
}

/**
 * 生成劳务清单项
 * @param {Object} laborData - 劳务数据
 * @param {number} level - 层级
 * @returns {LaborListItem} 劳务清单项
 */
function generateLaborListItem(laborData, level = 3) {
  const item = new LaborListItem({
    division: laborData.division,
    originalItem: laborData.itemName,
    workContent: laborData.feature || "",
    unit: laborData.unit,
    content: laborData.content,
    quantity: laborData.calculatedQuantity,
    unitPrice: laborData.unitPrice,
    amount: laborData.totalCost,
    taxRate: laborData.taxRate || 0.03,
    type: "item",
    level: level
  });
  
  // 设置Excel公式
  item.setFormula('content', `='3-分部分项成本测算'.G${laborData.rowIndex}`);
  item.setFormula('quantity', `='3-分部分项成本测算'.H${laborData.rowIndex}`);
  item.setFormula('amount', `=G${laborData.rowIndex}*H${laborData.rowIndex}`);
  
  return item;
}

/**
 * 处理分部工程分组
 * @param {Array} laborData - 劳务数据数组
 * @returns {Array} 分组后的劳务清单
 */
function processDivisionGrouping(laborData) {
  console.log(`  \x1b[36m[分部分组] \x1b[0m开始处理分部工程分组，共 \x1b[32m${laborData.length}\x1b[0m 个劳务项`);
  
  const laborList = [];
  let serialNo = 1;
  
  // 按分部工程分组
  const divisionGroups = groupByDivision(laborData);
  
  divisionGroups.forEach((divisionItems, division) => {
    console.log(`  \x1b[36m[分部] \x1b[0m处理分部: \x1b[32m${division}\x1b[0m，包含 \x1b[32m${divisionItems.length}\x1b[0m 个项目`);
    
    // 创建分部合计行
    const divisionTotalRow = createDivisionTotalRow(division, serialNo, divisionItems);
    laborList.push(divisionTotalRow);
    serialNo++;
    
    // 处理分部下的项目
    const processedItems = processNameMerging(divisionItems);
    processedItems.forEach(item => {
      laborList.push(item);
      if (item.type !== "merged") {
        serialNo++;
      }
    });
  });
  
  console.log(`  \x1b[36m[分部分组] \x1b[0m完成，生成 \x1b[32m${laborList.length}\x1b[0m 个清单项`);
  
  return laborList;
}

/**
 * 处理名称合并
 * @param {Array} divisionItems - 分部下的项目数组
 * @returns {Array} 处理后的项目数组
 */
function processNameMerging(divisionItems) {
  const processedItems = [];
  
  // 按名称+单位分组
  const nameGroups = groupByNameAndUnit(divisionItems);
  
  nameGroups.forEach((sameNameItems, nameUnitKey) => {
    const [itemName, unit] = nameUnitKey.split('_');
    
    if (sameNameItems.length > 1) {
      // 多个相同名称项目，创建合并行
      const mergedRow = createNameMergedRow(itemName, unit, sameNameItems);
      processedItems.push(mergedRow);
      
      // 添加子级明细项
      sameNameItems.forEach(item => {
        const detailItem = generateLaborListItem(item, 3);
        detailItem.parentId = mergedRow.id;
        processedItems.push(detailItem);
      });
    } else {
      // 单个项目，直接添加
      const singleItem = generateLaborListItem(sameNameItems[0], 2);
      processedItems.push(singleItem);
    }
  });
  
  return processedItems;
}

/**
 * 生成劳务清单
 * @param {Array} laborData - 劳务数据数组
 * @param {Object} options - 选项
 * @returns {Object} 劳务清单生成结果
 */
function generateLaborList(laborData, options = {}) {
  console.log(`\n\x1b[36m[劳务清单生成] \x1b[0m开始生成劳务清单，共 \x1b[32m${laborData.length}\x1b[0m 个项目`);
  
  try {
    // 1. 过滤劳务类型数据
    const labors = laborData.filter(item => item.costType === CostType.LABOR);
    
    if (labors.length === 0) {
      console.log(`  \x1b[33m[警告] \x1b[0m没有找到劳务类型的数据`);
      return {
        success: true,
        laborList: [],
        summary: { totalAmount: 0, totalQuantity: 0, itemCount: 0 },
        statistics: { totalItems: 0, divisionCount: 0, mergedItems: 0 }
      };
    }
    
    // 2. 处理分部工程分组
    const laborList = processDivisionGrouping(labors);
    
    // 3. 计算汇总信息
    const summary = calculateLaborSummary(laborList);
    
    // 4. 生成统计信息
    const statistics = {
      totalItems: laborList.length,
      divisionCount: laborList.filter(item => item.type === "division").length,
      mergedItems: laborList.filter(item => item.type === "merged").length,
      detailItems: laborList.filter(item => item.type === "item").length
    };
    
    console.log(`\x1b[36m[劳务清单生成] \x1b[0m生成完成，共 \x1b[32m${laborList.length}\x1b[0m 个清单项`);
    console.log(`  \x1b[36m[汇总] \x1b[0m总金额: \x1b[32m${summary.totalAmount.toFixed(2)}\x1b[0m 元`);
    console.log(`  \x1b[36m[统计] \x1b[0m分部: \x1b[32m${statistics.divisionCount}\x1b[0m, 合并项: \x1b[32m${statistics.mergedItems}\x1b[0m, 明细项: \x1b[32m${statistics.detailItems}\x1b[0m`);
    
    return {
      success: true,
      laborList,
      summary,
      statistics
    };
    
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m劳务清单生成失败: ${error.message}`);
    console.error(`\x1b[31m[错误堆栈] \x1b[0m${error.stack}`);
    return {
      success: false,
      error: error.message,
      laborList: []
    };
  }
}

/**
 * 计算劳务汇总信息
 * @param {Array} laborList - 劳务清单数组
 * @returns {Object} 汇总信息
 */
function calculateLaborSummary(laborList) {
  const summary = {
    totalAmount: 0,
    totalQuantity: 0,
    divisionTotals: {},
    itemCount: 0
  };
  
  laborList.forEach(item => {
    if (item.type === "division") {
      // 分部合计
      if (!summary.divisionTotals[item.division]) {
        summary.divisionTotals[item.division] = {
          amount: 0,
          count: 0
        };
      }
      summary.divisionTotals[item.division].amount += item.amount || 0;
      summary.totalAmount += item.amount || 0;
    } else if (item.type === "item" && !item.parentId) {
      // 独立明细项（非合并项的子项）
      summary.totalQuantity += item.quantity || 0;
      summary.itemCount++;
    }
  });
  
  return summary;
}

module.exports = {
  createDivisionTotalRow,
  createNameMergedRow,
  generateLaborListItem,
  processDivisionGrouping,
  processNameMerging,
  generateLaborList,
  calculateLaborSummary
};
