const path = require("path");
const {
  readCostCalculationExcel,
  getCostCalculationExcelFiles,
} = require("./excel-reader");
const {
  findTargetWorksheet,
  processCostCalculationWorksheet,
  validateProcessingResult,
} = require("./data-processor");

/**
 * 清单测算Excel解析器主控制器
 */

/**
 * 解析单个Excel文件
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} 解析结果
 */
async function parseExcelFile(filePath) {
  console.log(`\n\x1b[36m[开始] \x1b[0m解析Excel文件: \x1b[32m${path.basename(filePath)}\x1b[0m`);
  
  try {
    // 读取Excel文件
    const workbook = await readCostCalculationExcel(filePath);
    console.log(`  \x1b[36m[成功] \x1b[0m文件读取完成，共 \x1b[32m${workbook.worksheets.length}\x1b[0m 个工作表`);
    
    // 查找目标工作表
    const targetWorksheet = findTargetWorksheet(workbook);
    if (!targetWorksheet) {
      throw new Error("未找到包含'分部分项成本测算'的工作表");
    }
    
    // 处理工作表数据
    const costItems = processCostCalculationWorksheet(targetWorksheet);
    
    // 验证处理结果
    const validation = validateProcessingResult(costItems);
    
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.errors.join(", ")}`);
    }
    
    // 显示警告信息
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => {
        console.log(`  \x1b[33m[警告] \x1b[0m${warning}`);
      });
    }
    
    // 显示统计信息
    const stats = validation.statistics;
    console.log(`  \x1b[36m[统计] \x1b[0m目标项: \x1b[32m${stats.targetItems}\x1b[0m, 材料项: \x1b[32m${stats.materialItems}\x1b[0m, 劳务项: \x1b[32m${stats.laborItems}\x1b[0m, 专业分包项: \x1b[32m${stats.subcontractItems}\x1b[0m`);
    
    return {
      success: true,
      fileName: path.basename(filePath),
      filePath,
      projectInfo: {
        name: path.basename(filePath, path.extname(filePath)),
        fileName: path.basename(filePath),
        parseTime: new Date(),
        totalItems: costItems.length,
      },
      costItems,
      validation,
    };
    
  } catch (error) {
    console.error(`  \x1b[31m[错误] \x1b[0m解析文件失败: ${error.message}`);
    return {
      success: false,
      fileName: path.basename(filePath),
      filePath,
      error: error.message,
      costItems: [],
    };
  }
}

/**
 * 解析目录中的所有Excel文件
 * @param {string} inputDir - 输入目录路径
 * @returns {Promise<Array>} 解析结果数组
 */
async function parseAllExcelFiles(inputDir) {
  console.log(`\n\x1b[36m[扫描] \x1b[0m扫描目录: \x1b[32m${inputDir}\x1b[0m`);
  
  // 获取所有Excel文件
  const excelFiles = getCostCalculationExcelFiles(inputDir);
  
  if (excelFiles.length === 0) {
    console.log(`  \x1b[33m[提示] \x1b[0m目录中没有找到Excel文件`);
    return [];
  }
  
  console.log(`  \x1b[36m[发现] \x1b[0m找到 \x1b[32m${excelFiles.length}\x1b[0m 个Excel文件`);
  
  const results = [];
  
  // 逐个解析文件
  for (let i = 0; i < excelFiles.length; i++) {
    const fileName = excelFiles[i];
    const filePath = path.join(inputDir, fileName);
    
    console.log(`\n\x1b[36m[进度] \x1b[0m(${i + 1}/${excelFiles.length}) 处理文件: \x1b[32m${fileName}\x1b[0m`);
    
    const result = await parseExcelFile(filePath);
    results.push(result);
    
    if (result.success) {
      console.log(`  \x1b[32m[完成] \x1b[0m文件解析成功`);
    } else {
      console.log(`  \x1b[31m[失败] \x1b[0m文件解析失败`);
    }
  }
  
  // 汇总统计
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  console.log(`\n\x1b[36m[汇总] \x1b[0m解析完成: 成功 \x1b[32m${successCount}\x1b[0m 个, 失败 \x1b[31m${failureCount}\x1b[0m 个`);
  
  return results;
}

/**
 * 合并多个文件的解析结果
 * @param {Array} parseResults - 解析结果数组
 * @returns {Object} 合并后的结果
 */
function mergeParseResults(parseResults) {
  const successResults = parseResults.filter(r => r.success);
  
  if (successResults.length === 0) {
    return {
      success: false,
      error: "没有成功解析的文件",
      costItems: [],
    };
  }
  
  // 合并所有成本项
  const allCostItems = [];
  const projectInfos = [];
  
  successResults.forEach(result => {
    // 为每个成本项添加来源文件信息
    const itemsWithSource = result.costItems.map(item => ({
      ...item,
      sourceFile: result.fileName,
      sourceFilePath: result.filePath,
    }));
    
    allCostItems.push(...itemsWithSource);
    projectInfos.push(result.projectInfo);
  });
  
  // 生成合并后的项目信息
  const mergedProjectInfo = {
    name: "合并项目",
    fileCount: successResults.length,
    fileNames: successResults.map(r => r.fileName),
    parseTime: new Date(),
    totalItems: allCostItems.length,
  };
  
  // 统计信息
  const statistics = {
    totalItems: allCostItems.length,
    targetItems: allCostItems.filter(item => item.costType === "目标").length,
    materialItems: allCostItems.filter(item => item.costType === "材").length,
    laborItems: allCostItems.filter(item => item.costType === "人").length,
    subcontractItems: allCostItems.filter(item => item.costType === "专").length,
    fileCount: successResults.length,
    divisions: [...new Set(allCostItems.map(item => item.divisionalWork).filter(d => d))],
  };
  
  console.log(`\n\x1b[36m[合并] \x1b[0m合并完成:`);
  console.log(`  总计: \x1b[32m${statistics.totalItems}\x1b[0m 个成本项`);
  console.log(`  目标项: \x1b[32m${statistics.targetItems}\x1b[0m, 材料项: \x1b[32m${statistics.materialItems}\x1b[0m`);
  console.log(`  劳务项: \x1b[32m${statistics.laborItems}\x1b[0m, 专业分包项: \x1b[32m${statistics.subcontractItems}\x1b[0m`);
  console.log(`  分部工程: \x1b[32m${statistics.divisions.length}\x1b[0m 个`);
  
  return {
    success: true,
    projectInfo: mergedProjectInfo,
    costItems: allCostItems,
    statistics,
    sourceFiles: successResults.map(r => ({
      fileName: r.fileName,
      filePath: r.filePath,
      itemCount: r.costItems.length,
    })),
  };
}

/**
 * 主解析函数
 * @param {string} inputDir - 输入目录路径
 * @returns {Promise<Object>} 最终解析结果
 */
async function parseInputDirectory(inputDir) {
  try {
    console.log(`\x1b[36m==================================================\x1b[0m`);
    console.log(`\x1b[32m           开始解析清单测算数据\x1b[0m`);
    console.log(`\x1b[36m==================================================\x1b[0m`);
    
    // 解析所有文件
    const parseResults = await parseAllExcelFiles(inputDir);
    
    if (parseResults.length === 0) {
      return {
        success: false,
        error: "输入目录中没有找到Excel文件",
        costItems: [],
      };
    }
    
    // 合并结果
    const mergedResult = mergeParseResults(parseResults);
    
    console.log(`\x1b[36m==================================================\x1b[0m`);
    console.log(`\x1b[32m           解析完成\x1b[0m`);
    console.log(`\x1b[36m==================================================\x1b[0m`);
    
    return mergedResult;
    
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m解析过程中发生错误: ${error.message}`);
    return {
      success: false,
      error: error.message,
      costItems: [],
    };
  }
}

module.exports = {
  parseExcelFile,
  parseAllExcelFiles,
  mergeParseResults,
  parseInputDirectory,
};
