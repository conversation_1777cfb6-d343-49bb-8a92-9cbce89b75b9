const fs = require("fs");
const path = require("path");

// 获取应用程序的根目录
const isPackaged = process.pkg !== undefined;
const rootDir = isPackaged
  ? path.dirname(process.execPath)
  : path.dirname(path.dirname(__dirname));

// 获取应用程序版本号
const packageJson = require("../../package.json");
const appVersion = packageJson.version;

// 创建必要的目录结构 - 清单测算功能专用
const inputDir = path.join(rootDir, "input", "bill-calculation");
const outputDir = path.join(rootDir, "output", "bill-calculation");

// 确保目录存在
function ensureDirectories() {
  [inputDir, outputDir].forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// 显示欢迎信息
function showWelcomeMessage() {
  console.log(
    "\n\x1b[36m==================================================\x1b[0m"
  );
  console.log(`\x1b[36m    欢迎使用清单测算工具 v${appVersion}\x1b[0m`);
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log(
    "\x1b[32m本工具将自动读取 input/bill-calculation 文件夹中的清单文件，进行价格测算和分析，\n最终将测算报告输出在 output/bill-calculation 文件夹下。\x1b[0m"
  );
  console.log(
    "\x1b[33m使用方法：将清单Excel文件放在 input/bill-calculation 文件夹下，然后运行本程序。\x1b[0m"
  );
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
}

/**
 * 等待用户输入后退出
 */
function waitForUserExit() {
  console.log("\x1b[33m按回车键退出...\x1b[0m");

  // 检查是否在TTY环境下
  if (process.stdin.isTTY) {
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on("data", process.exit.bind(process, 0));
  } else {
    // 非TTY环境下，直接退出
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  }
}

/**
 * 清单测算功能主函数
 */
async function calculateBill() {
  try {
    // 确保目录存在
    ensureDirectories();

    // 显示欢迎信息
    showWelcomeMessage();

    console.log("\x1b[36m[清单测算] \x1b[33m功能开发中...\x1b[0m");
    console.log("\x1b[33m此功能将用于清单价格测算和分析\x1b[0m");

    // TODO: 实现清单测算功能
    // 1. 读取清单数据
    // 2. 进行价格测算
    // 3. 生成测算报告

    waitForUserExit();
  } catch (error) {
    console.error(
      `\n\x1b[31m[错误] \x1b[0m处理文件时出错: ${error.message}\x1b[0m`
    );
    waitForUserExit();
  }
}

module.exports = {
  calculateBill,
};
