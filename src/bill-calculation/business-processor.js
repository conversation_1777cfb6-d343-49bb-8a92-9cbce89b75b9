const { CostType, CalculatedData } = require("./types");
const { BusinessRules } = require("./config");
const {
  groupByDivision,
  extractSteelNumber,
  extractConcreteGrade,
  extractAdditives,
  getMaterialCategory,
  safeParseNumber,
} = require("./utils");

/**
 * 业务逻辑处理器
 * 实现核心业务逻辑，包括数据分类、排序、计算等
 */

/**
 * 数据分类器 - 按成本类型分类
 * @param {Array} userInputData - 用户输入数据数组
 * @returns {Object} 分类结果
 */
function classifyDataByCostType(userInputData) {
  const classified = {
    targets: [],
    materials: [],
    labors: [],
    subcontracts: [],
    others: [],
  };

  userInputData.forEach((item) => {
    switch (item.costType) {
      case CostType.TARGET:
        classified.targets.push(item);
        break;
      case CostType.MATERIAL:
        classified.materials.push(item);
        break;
      case CostType.LABOR:
        classified.labors.push(item);
        break;
      case CostType.SUBCONTRACT:
        classified.subcontracts.push(item);
        break;
      default:
        classified.others.push(item);
        break;
    }
  });

  console.log(
    `  \x1b[36m[分类] \x1b[0m目标项: \x1b[32m${classified.targets.length}\x1b[0m, 材料项: \x1b[32m${classified.materials.length}\x1b[0m, 劳务项: \x1b[32m${classified.labors.length}\x1b[0m, 专业分包项: \x1b[32m${classified.subcontracts.length}\x1b[0m`
  );

  return classified;
}

/**
 * 材料排序器 - 按业务规则排序材料
 * @param {Array} materials - 材料数组
 * @returns {Array} 排序后的材料数组
 */
function sortMaterials(materials) {
  console.log(
    `  \x1b[36m[排序] \x1b[0m开始材料排序，共 \x1b[32m${materials.length}\x1b[0m 个材料`
  );

  // 1. 按类别分组
  const categorized = {
    steel: [],
    concrete: [],
    mortar: [],
    brick: [],
    others: [],
  };

  materials.forEach((material) => {
    const category = getMaterialCategory(material.itemName);
    switch (category) {
      case "钢筋类":
        categorized.steel.push(material);
        break;
      case "混凝土类":
        categorized.concrete.push(material);
        break;
      case "砂浆类":
        categorized.mortar.push(material);
        break;
      case "砖类":
        categorized.brick.push(material);
        break;
      default:
        categorized.others.push(material);
        break;
    }
  });

  // 2. 钢筋类按直径数字排序
  categorized.steel.sort((a, b) => {
    const numA = extractSteelNumber(a.itemName);
    const numB = extractSteelNumber(b.itemName);
    return numA - numB;
  });

  // 3. 混凝土类按标号排序
  categorized.concrete.sort((a, b) => {
    const gradeA = extractConcreteGrade(a.itemName);
    const gradeB = extractConcreteGrade(b.itemName);
    return gradeA - gradeB;
  });

  // 4. 其他类别保持原顺序

  const sortedMaterials = [
    ...categorized.steel,
    ...categorized.concrete,
    ...categorized.mortar,
    ...categorized.brick,
    ...categorized.others,
  ];

  console.log(
    `  \x1b[36m[排序] \x1b[0m材料排序完成: 钢筋 \x1b[32m${categorized.steel.length}\x1b[0m, 混凝土 \x1b[32m${categorized.concrete.length}\x1b[0m, 砂浆 \x1b[32m${categorized.mortar.length}\x1b[0m, 砖 \x1b[32m${categorized.brick.length}\x1b[0m, 其他 \x1b[32m${categorized.others.length}\x1b[0m`
  );

  return sortedMaterials;
}

/**
 * 商品混凝土定价计算器
 * @param {string} concreteName - 混凝土名称
 * @param {number} basePrice - C30基准价格
 * @returns {number} 计算后的价格
 */
function calculateConcretePrice(concreteName, basePrice = 300) {
  const grade = extractConcreteGrade(concreteName);
  const additives = extractAdditives(concreteName);

  let price = basePrice; // C30基准价

  // 标号差价
  const gradePricing = BusinessRules.concretePricing.gradePricing;
  const gradeKey = grade.toString();
  if (gradePricing[gradeKey] !== undefined) {
    price += gradePricing[gradeKey];
  }

  // 附加费
  const additiveDict = BusinessRules.concretePricing.additives;
  if (additives && Array.isArray(additives)) {
    additives.forEach((additive) => {
      if (additiveDict[additive]) {
        price += additiveDict[additive];
      }
    });
  }

  // 泵送费规则：C15/C20/C25不含泵送时减10
  if (
    grade <= BusinessRules.concretePricing.pumpingRules.lowGradeThreshold &&
    additives &&
    !additives.includes("泵送")
  ) {
    price -= BusinessRules.concretePricing.pumpingRules.lowGradeReduction;
  }

  return price;
}

/**
 * 关联关系建立器 - 建立目标项与明细项的关联
 * @param {Array} targets - 目标项数组
 * @param {Array} details - 明细项数组（材料+劳务+专业分包）
 * @returns {Map} 关联关系映射
 */
function buildRelationships(targets, details) {
  const relationships = new Map();

  // 按分部工程分组
  const targetsByDivision = groupByDivision(targets);
  const detailsByDivision = groupByDivision(details);

  targetsByDivision.forEach((divisionTargets, division) => {
    const divisionDetails = detailsByDivision.get(division) || [];

    divisionTargets.forEach((target) => {
      // 找到属于这个目标项的明细项（通过行号范围判断）
      const relatedDetails = findRelatedDetails(target, divisionDetails);
      relationships.set(target.id, relatedDetails);
    });
  });

  console.log(
    `  \x1b[36m[关联] \x1b[0m建立了 \x1b[32m${relationships.size}\x1b[0m 个目标项的关联关系`
  );

  return relationships;
}

/**
 * 查找相关明细项
 * @param {Object} target - 目标项
 * @param {Array} details - 明细项数组
 * @returns {Array} 相关明细项
 */
function findRelatedDetails(target, details) {
  // 简化逻辑：找到目标项之后、下一个目标项之前的所有明细项
  const targetRowIndex = target.rowIndex;

  // 找到下一个目标项的行号
  let nextTargetRowIndex = Infinity;
  details.forEach((detail) => {
    if (
      detail.costType === CostType.TARGET &&
      detail.rowIndex > targetRowIndex
    ) {
      nextTargetRowIndex = Math.min(nextTargetRowIndex, detail.rowIndex);
    }
  });

  // 返回在范围内的明细项
  return details.filter(
    (detail) =>
      detail.rowIndex > targetRowIndex &&
      detail.rowIndex < nextTargetRowIndex &&
      detail.costType !== CostType.TARGET
  );
}

/**
 * 成本计算器 - 计算测算成本量和金额
 * @param {Map} relationships - 关联关系映射
 * @returns {Array} 计算结果数组
 */
function calculateCosts(relationships) {
  const calculatedData = [];

  relationships.forEach((details, targetId) => {
    // 找到目标项
    const target = Array.from(relationships.keys()).find(
      (key) => key === targetId
    );
    if (!target) return;

    // 为每个明细项计算成本
    details.forEach((detail) => {
      const calculated = new CalculatedData(detail);

      // 计算测算成本量：父级工程量 × 本级含量
      calculated.calculatedQuantity = target.quantity * detail.content;

      // 设置成本分配（根据成本类型）
      switch (detail.costType) {
        case CostType.MATERIAL:
          calculated.materialCost =
            calculated.calculatedQuantity * calculated.unitPrice;
          calculated.laborCost = 0;
          calculated.subcontractCost = 0;
          break;
        case CostType.LABOR:
          calculated.laborCost =
            calculated.calculatedQuantity * calculated.unitPrice;
          calculated.materialCost = 0;
          calculated.subcontractCost = 0;
          break;
        case CostType.SUBCONTRACT:
          calculated.subcontractCost =
            calculated.calculatedQuantity * calculated.unitPrice;
          calculated.materialCost = 0;
          calculated.laborCost = 0;
          break;
      }

      // 计算总成本
      calculated.totalCost =
        calculated.laborCost +
        calculated.materialCost +
        calculated.subcontractCost;

      calculatedData.push(calculated);
    });
  });

  console.log(
    `  \x1b[36m[计算] \x1b[0m完成 \x1b[32m${calculatedData.length}\x1b[0m 个项目的成本计算`
  );

  return calculatedData;
}

/**
 * 数据验证器 - 验证计算结果
 * @param {Array} calculatedData - 计算结果数组
 * @returns {Object} 验证结果
 */
function validateCalculatedData(calculatedData) {
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
    statistics: {
      totalItems: calculatedData.length,
      validItems: 0,
      invalidItems: 0,
      totalAmount: 0,
    },
  };

  calculatedData.forEach((item, index) => {
    let isItemValid = true;

    // 检查必要字段
    if (!item.itemName) {
      validation.errors.push(`第${index + 1}项：项目名称不能为空`);
      isItemValid = false;
    }

    if (!item.unit) {
      validation.errors.push(`第${index + 1}项：计量单位不能为空`);
      isItemValid = false;
    }

    // 检查数值合理性
    if (item.calculatedQuantity < 0) {
      validation.warnings.push(`第${index + 1}项：测算成本量为负数`);
    }

    if (item.totalCost < 0) {
      validation.warnings.push(`第${index + 1}项：总成本为负数`);
    }

    if (isItemValid) {
      validation.statistics.validItems++;
      validation.statistics.totalAmount += item.totalCost;
    } else {
      validation.statistics.invalidItems++;
    }
  });

  if (validation.errors.length > 0) {
    validation.isValid = false;
  }

  return validation;
}

/**
 * 业务逻辑处理主函数
 * @param {Array} userInputData - 用户输入数据数组
 * @returns {Object} 处理结果
 */
function processBusinessLogic(userInputData) {
  console.log(
    `\n\x1b[36m[业务处理] \x1b[0m开始处理业务逻辑，共 \x1b[32m${userInputData.length}\x1b[0m 个项目`
  );

  try {
    // 1. 数据分类
    const classified = classifyDataByCostType(userInputData);

    // 2. 材料排序
    const sortedMaterials = sortMaterials(classified.materials);

    // 3. 建立关联关系
    const allDetails = [
      ...sortedMaterials,
      ...classified.labors,
      ...classified.subcontracts,
    ];
    const relationships = buildRelationships(classified.targets, allDetails);

    // 4. 成本计算
    const calculatedData = calculateCosts(relationships);

    // 5. 数据验证
    const validation = validateCalculatedData(calculatedData);

    console.log(
      `\x1b[36m[业务处理] \x1b[0m处理完成，生成 \x1b[32m${calculatedData.length}\x1b[0m 个计算结果`
    );

    return {
      success: true,
      classified,
      sortedMaterials,
      relationships,
      calculatedData,
      validation,
    };
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m业务逻辑处理失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      calculatedData: [],
    };
  }
}

module.exports = {
  classifyDataByCostType,
  sortMaterials,
  calculateConcretePrice,
  buildRelationships,
  findRelatedDetails,
  calculateCosts,
  validateCalculatedData,
  processBusinessLogic,
};
