const path = require("path");
const fs = require("fs");

// 获取应用程序的根目录
const isPackaged = process.pkg !== undefined;
const rootDir = isPackaged
  ? path.dirname(process.execPath)
  : path.dirname(path.dirname(__dirname));

// 获取应用程序版本号
const packageJson = require("../../package.json");
const appVersion = packageJson.version;

// 创建必要的目录结构 - 清单测算功能专用
const inputDir = path.join(rootDir, "input", "bill-calculation");
const outputDir = path.join(rootDir, "output", "bill-calculation");

// 确保目录存在
function ensureDirectories() {
  [inputDir, outputDir].forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// 业务规则配置
const BusinessRules = {
  // 材料排序规则
  materialSorting: {
    // 排序优先级
    priority: ["钢筋类", "混凝土类", "砂浆类", "砖类", "其他"],
    
    // 钢筋识别和排序
    steel: {
      keywords: ["钢筋", "Φ"],
      numberPattern: /Φ(\d+)/,
      sortByNumber: true
    },
    
    // 混凝土识别和分组
    concrete: {
      keywords: ["混凝土", "商品混凝土"],
      gradePattern: /C(\d+)/,
      needsGrouping: true,
      parentName: "商品混凝土"
    },
    
    // 砂浆识别
    mortar: {
      keywords: ["砂浆", "预拌砂浆"]
    },
    
    // 砖类识别
    brick: {
      keywords: ["砖", "砌块"]
    }
  },

  // 商品混凝土定价规则
  concretePricing: {
    // 基准标号和价格
    baseGrade: 30,
    
    // 标号差价规则
    gradePricing: {
      "15": -10, "20": -10, "25": -10,
      "30": 0,   // 基准
      "35": 15,  "40": 15,
      "45": 25,  "50": 25,
      "55": 55
    },
    
    // 附加费字典
    additives: {
      "细石": 5,
      "泵送": 10,
      "抗渗": 15,
      "抗冻": 20,
      "早强": 25,
      "缓凝": 15,
      "膨胀": 30
    },
    
    // 泵送费规则
    pumpingRules: {
      // C15/C20/C25不含泵送时减10
      lowGradeReduction: 10,
      lowGradeThreshold: 25
    }
  },

  // 计算公式配置
  formulas: {
    // 测算成本量 = 父级工程量 × 本级含量
    calculatedQuantity: (parentRow, currentRow) => `=F${parentRow}*G${currentRow}`,
    
    // 测算金额 = 测算成本量 × 测算单价
    amount: (row) => `=G${row}*H${row}`,
    
    // 测算成本合计 = 劳务费 + 材料费 + 专业分包
    totalCost: (row) => `=K${row}+L${row}+M${row}`,
    
    // 跨表引用
    crossSheetReference: (sheetName, itemName, column) => 
      `=IFERROR(INDEX(${sheetName}.${column}:${column},MATCH("${itemName}",${sheetName}.C:C,0)),0)`,
    
    // 区域求和
    sumRange: (startRow, endRow, column) => `=SUM(${column}${startRow}:${column}${endRow})`
  },

  // 输出格式配置
  outputFormat: {
    // 工作表名称
    sheets: {
      costCalculation: "3-分部分项成本测算",
      materialList: "4-材料清单",
      laborList: "4-劳务清单", 
      subcontractList: "4-专业分包清单"
    },
    
    // 列配置
    columns: {
      // 分部分项成本测算表列配置
      costCalculation: {
        division: "A",           // 分部工程
        costType: "B",          // 成本类型
        itemName: "C",          // 项目名称
        feature: "D",           // 项目特征
        unit: "E",              // 计量单位
        quantity: "F",          // 合并后清单工程量
        content: "G",           // 含量
        calculatedQuantity: "H", // 测算成本量
        unitPrice: "I",         // 测算单价
        totalCost: "J",         // 测算成本合计
        laborCost: "K",         // 劳务费
        materialCost: "L",      // 材料费
        subcontractCost: "M",   // 专业分包
        comprehensiveUnitPrice: "N", // 测算综合单价
        taxRate: "O",           // 税率
        taxAmount: "P",         // 进项税额
        remarks: "Q"            // 备注
      },
      
      // 清单表列配置（材料/劳务/专业分包通用）
      list: {
        serialNo: "A",          // 序号
        division: "B",          // 分部工程
        originalItem: "C",      // 原测算清单
        specification: "D",     // 规格型号/工作内容
        unit: "E",              // 计量单位
        content: "F",           // 测算含量
        quantity: "G",          // 测算成本量
        unitPrice: "H",         // 测算单价
        amount: "I",            // 测算金额
        taxRate: "J"            // 税率
      }
    },
    
    // 表头配置
    headers: {
      costCalculation: [
        "分部工程", "成本类型", "项目名称", "项目特征", "计量单位", 
        "合并后清单工程量", "含量", "测算成本量", "测算单价(含税)", 
        "测算成本合计", "劳务费", "材料费", "专业分包", 
        "测算综合单价", "税率", "进项税额", "备注"
      ],
      materialList: [
        "序号", "分部工程", "原测算清单", "规格型号", "计量单位",
        "测算含量", "测算成本量", "测算单价", "测算金额", "税率(%)"
      ],
      laborList: [
        "序号", "分部工程", "原测算清单", "工作内容", "计量单位",
        "测算含量", "测算成本量", "测算单价", "测算金额", "税率(%)"
      ],
      subcontractList: [
        "序号", "分部工程", "原测算清单", "工作内容", "计量单位",
        "测算含量", "测算成本量", "测算单价", "测算金额", "税率(%)"
      ]
    }
  },

  // 数据验证规则
  validation: {
    required: ["costType", "itemName", "unit"],
    numeric: ["quantity", "content", "unitPrice"],
    costTypes: ["目标", "人", "材", "专"],
    maxLength: {
      itemName: 100,
      feature: 500,
      unit: 20
    }
  }
};

// 显示欢迎信息
function showWelcomeMessage() {
  console.log(
    "\n\x1b[36m==================================================\x1b[0m"
  );
  console.log(`\x1b[36m    欢迎使用清单测算工具 v${appVersion}\x1b[0m`);
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log(
    "\x1b[32m本工具将自动读取 input/bill-calculation 文件夹中的清单文件，进行价格测算和分析，\n最终将测算报告输出在 output/bill-calculation 文件夹下。\x1b[0m"
  );
  console.log(
    "\x1b[33m使用方法：将清单Excel文件放在 input/bill-calculation 文件夹下，然后运行本程序。\x1b[0m"
  );
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
}

module.exports = {
  rootDir,
  inputDir,
  outputDir,
  appVersion,
  BusinessRules,
  ensureDirectories,
  showWelcomeMessage
};
