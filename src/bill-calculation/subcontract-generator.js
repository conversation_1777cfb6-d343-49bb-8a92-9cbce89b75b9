const { SubcontractListItem, CostType } = require("./types");
const { 
  createDivisionTotalRow,
  createNameMergedRow,
  processDivisionGrouping,
  processNameMerging,
  calculateLaborSummary
} = require("./labor-generator");
const { 
  groupByDivision,
  groupByNameAndUnit,
  generateId
} = require("./utils");

/**
 * 专业分包清单生成器
 * 复用劳务清单逻辑，实现专业分包清单生成
 */

/**
 * 生成专业分包清单项
 * @param {Object} subcontractData - 专业分包数据
 * @param {number} level - 层级
 * @returns {SubcontractListItem} 专业分包清单项
 */
function generateSubcontractListItem(subcontractData, level = 3) {
  const item = new SubcontractListItem({
    division: subcontractData.division,
    originalItem: subcontractData.itemName,
    workContent: subcontractData.feature || "",
    unit: subcontractData.unit,
    content: subcontractData.content,
    quantity: subcontractData.calculatedQuantity,
    unitPrice: subcontractData.unitPrice,
    amount: subcontractData.totalCost,
    taxRate: subcontractData.taxRate || 0.09, // 专业分包通常税率为9%
    type: "item",
    level: level
  });
  
  // 设置Excel公式
  item.setFormula('content', `='3-分部分项成本测算'.G${subcontractData.rowIndex}`);
  item.setFormula('quantity', `='3-分部分项成本测算'.H${subcontractData.rowIndex}`);
  item.setFormula('amount', `=G${subcontractData.rowIndex}*H${subcontractData.rowIndex}`);
  
  return item;
}

/**
 * 创建专业分包分部合计行
 * @param {string} division - 分部工程名称
 * @param {number} serialNo - 序号
 * @param {Array} divisionItems - 分部下的所有项目
 * @returns {SubcontractListItem} 分部合计行
 */
function createSubcontractDivisionTotalRow(division, serialNo, divisionItems) {
  const totalRow = new SubcontractListItem({
    serialNo: serialNo.toString(),
    division: division,
    originalItem: division,
    workContent: "",
    unit: "",
    content: 0,
    quantity: 0,
    unitPrice: 0,
    amount: 0,
    taxRate: 0,
    type: "division",
    level: 1
  });
  
  // 设置合计公式
  if (divisionItems.length > 0) {
    const startRow = divisionItems[0].rowIndex + 1; // Excel行号从1开始
    const endRow = divisionItems[divisionItems.length - 1].rowIndex + 1;
    totalRow.setFormula('amount', `=SUM(I${startRow}:I${endRow})`);
  }
  
  return totalRow;
}

/**
 * 创建专业分包名称合并行
 * @param {string} itemName - 项目名称
 * @param {string} unit - 计量单位
 * @param {Array} sameNameItems - 相同名称的项目数组
 * @returns {SubcontractListItem} 名称合并行
 */
function createSubcontractNameMergedRow(itemName, unit, sameNameItems) {
  const mergedRow = new SubcontractListItem({
    division: sameNameItems[0].division,
    originalItem: itemName,
    workContent: sameNameItems[0].feature || "",
    unit: unit,
    content: 0,
    quantity: 0,
    unitPrice: 0,
    amount: 0,
    taxRate: sameNameItems[0].taxRate || 0.09,
    type: "merged",
    level: 2
  });
  
  // 计算合并数量和金额
  let totalQuantity = 0;
  let totalAmount = 0;
  
  sameNameItems.forEach(item => {
    totalQuantity += item.calculatedQuantity || 0;
    totalAmount += item.totalCost || 0;
  });
  
  mergedRow.quantity = totalQuantity;
  mergedRow.amount = totalAmount;
  
  // 设置Excel公式
  if (sameNameItems.length > 1) {
    const childRowNumbers = sameNameItems.map(item => item.rowIndex + 1);
    const quantityFormula = childRowNumbers.map(row => `G${row}`).join('+');
    const amountFormula = childRowNumbers.map(row => `I${row}`).join('+');
    
    mergedRow.setFormula('quantity', `=${quantityFormula}`);
    mergedRow.setFormula('amount', `=${amountFormula}`);
  }
  
  return mergedRow;
}

/**
 * 处理专业分包名称合并
 * @param {Array} divisionItems - 分部下的项目数组
 * @returns {Array} 处理后的项目数组
 */
function processSubcontractNameMerging(divisionItems) {
  const processedItems = [];
  
  // 按名称+单位分组
  const nameGroups = groupByNameAndUnit(divisionItems);
  
  nameGroups.forEach((sameNameItems, nameUnitKey) => {
    const [itemName, unit] = nameUnitKey.split('_');
    
    if (sameNameItems.length > 1) {
      // 多个相同名称项目，创建合并行
      const mergedRow = createSubcontractNameMergedRow(itemName, unit, sameNameItems);
      processedItems.push(mergedRow);
      
      // 添加子级明细项
      sameNameItems.forEach(item => {
        const detailItem = generateSubcontractListItem(item, 3);
        detailItem.parentId = mergedRow.id;
        processedItems.push(detailItem);
      });
    } else {
      // 单个项目，直接添加
      const singleItem = generateSubcontractListItem(sameNameItems[0], 2);
      processedItems.push(singleItem);
    }
  });
  
  return processedItems;
}

/**
 * 处理专业分包分部工程分组
 * @param {Array} subcontractData - 专业分包数据数组
 * @returns {Array} 分组后的专业分包清单
 */
function processSubcontractDivisionGrouping(subcontractData) {
  console.log(`  \x1b[36m[分部分组] \x1b[0m开始处理分部工程分组，共 \x1b[32m${subcontractData.length}\x1b[0m 个专业分包项`);
  
  const subcontractList = [];
  let serialNo = 1;
  
  // 按分部工程分组
  const divisionGroups = groupByDivision(subcontractData);
  
  divisionGroups.forEach((divisionItems, division) => {
    console.log(`  \x1b[36m[分部] \x1b[0m处理分部: \x1b[32m${division}\x1b[0m，包含 \x1b[32m${divisionItems.length}\x1b[0m 个项目`);
    
    // 创建分部合计行
    const divisionTotalRow = createSubcontractDivisionTotalRow(division, serialNo, divisionItems);
    subcontractList.push(divisionTotalRow);
    serialNo++;
    
    // 处理分部下的项目
    const processedItems = processSubcontractNameMerging(divisionItems);
    processedItems.forEach(item => {
      subcontractList.push(item);
      if (item.type !== "merged") {
        serialNo++;
      }
    });
  });
  
  console.log(`  \x1b[36m[分部分组] \x1b[0m完成，生成 \x1b[32m${subcontractList.length}\x1b[0m 个清单项`);
  
  return subcontractList;
}

/**
 * 生成专业分包清单
 * @param {Array} subcontractData - 专业分包数据数组
 * @param {Object} options - 选项
 * @returns {Object} 专业分包清单生成结果
 */
function generateSubcontractList(subcontractData, options = {}) {
  console.log(`\n\x1b[36m[专业分包清单生成] \x1b[0m开始生成专业分包清单，共 \x1b[32m${subcontractData.length}\x1b[0m 个项目`);
  
  try {
    // 1. 过滤专业分包类型数据
    const subcontracts = subcontractData.filter(item => item.costType === CostType.SUBCONTRACT);
    
    if (subcontracts.length === 0) {
      console.log(`  \x1b[33m[警告] \x1b[0m没有找到专业分包类型的数据`);
      return {
        success: true,
        subcontractList: [],
        summary: { totalAmount: 0, totalQuantity: 0, itemCount: 0 },
        statistics: { totalItems: 0, divisionCount: 0, mergedItems: 0 }
      };
    }
    
    // 2. 处理分部工程分组
    const subcontractList = processSubcontractDivisionGrouping(subcontracts);
    
    // 3. 计算汇总信息
    const summary = calculateSubcontractSummary(subcontractList);
    
    // 4. 生成统计信息
    const statistics = {
      totalItems: subcontractList.length,
      divisionCount: subcontractList.filter(item => item.type === "division").length,
      mergedItems: subcontractList.filter(item => item.type === "merged").length,
      detailItems: subcontractList.filter(item => item.type === "item").length
    };
    
    console.log(`\x1b[36m[专业分包清单生成] \x1b[0m生成完成，共 \x1b[32m${subcontractList.length}\x1b[0m 个清单项`);
    console.log(`  \x1b[36m[汇总] \x1b[0m总金额: \x1b[32m${summary.totalAmount.toFixed(2)}\x1b[0m 元`);
    console.log(`  \x1b[36m[统计] \x1b[0m分部: \x1b[32m${statistics.divisionCount}\x1b[0m, 合并项: \x1b[32m${statistics.mergedItems}\x1b[0m, 明细项: \x1b[32m${statistics.detailItems}\x1b[0m`);
    
    return {
      success: true,
      subcontractList,
      summary,
      statistics
    };
    
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m专业分包清单生成失败: ${error.message}`);
    console.error(`\x1b[31m[错误堆栈] \x1b[0m${error.stack}`);
    return {
      success: false,
      error: error.message,
      subcontractList: []
    };
  }
}

/**
 * 计算专业分包汇总信息
 * @param {Array} subcontractList - 专业分包清单数组
 * @returns {Object} 汇总信息
 */
function calculateSubcontractSummary(subcontractList) {
  const summary = {
    totalAmount: 0,
    totalQuantity: 0,
    divisionTotals: {},
    itemCount: 0
  };
  
  subcontractList.forEach(item => {
    if (item.type === "division") {
      // 分部合计
      if (!summary.divisionTotals[item.division]) {
        summary.divisionTotals[item.division] = {
          amount: 0,
          count: 0
        };
      }
      summary.divisionTotals[item.division].amount += item.amount || 0;
      summary.totalAmount += item.amount || 0;
    } else if (item.type === "item" && !item.parentId) {
      // 独立明细项（非合并项的子项）
      summary.totalQuantity += item.quantity || 0;
      summary.itemCount++;
    }
  });
  
  return summary;
}

module.exports = {
  generateSubcontractListItem,
  createSubcontractDivisionTotalRow,
  createSubcontractNameMergedRow,
  processSubcontractNameMerging,
  processSubcontractDivisionGrouping,
  generateSubcontractList,
  calculateSubcontractSummary
};
