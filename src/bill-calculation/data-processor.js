const {
  getCostCalculationColumnMapping,
  applyDefaultCostCalculationMapping,
  parseCostType,
  parseItemName,
  safeParseNumber,
  createCostItem,
  isDivisionRow,
  isValidCostItem,
} = require("./excel-reader");

/**
 * 清单测算数据处理器
 * 用于处理"3-分部分项成本测算"工作表数据
 */

/**
 * 查找目标工作表
 * @param {Object} workbook - Excel工作簿对象
 * @returns {Object|null} 目标工作表对象
 */
function findTargetWorksheet(workbook) {
  // 查找名称包含"分部分项成本测算"但不包含"规则"的工作表
  let targetWorksheet = null;

  workbook.eachSheet((worksheet) => {
    const sheetName = worksheet.name;
    if (
      sheetName.includes("分部分项") &&
      sheetName.includes("成本测算") &&
      !sheetName.includes("规则")
    ) {
      targetWorksheet = worksheet;
      console.log(
        `  \x1b[36m[发现] \x1b[0m找到目标工作表: \x1b[32m${worksheet.name}\x1b[0m`
      );
      return; // 找到后立即返回
    }
  });

  // 如果没找到，尝试查找包含"3-"但不包含"规则"的工作表
  if (!targetWorksheet) {
    workbook.eachSheet((worksheet) => {
      const sheetName = worksheet.name;
      if (
        (sheetName.includes("3-") || sheetName.includes("3 ")) &&
        !sheetName.includes("规则")
      ) {
        targetWorksheet = worksheet;
        console.log(
          `  \x1b[36m[发现] \x1b[0m找到可能的目标工作表: \x1b[32m${worksheet.name}\x1b[0m`
        );
        return; // 找到后立即返回
      }
    });
  }

  return targetWorksheet;
}

/**
 * 分析工作表结构，确定表头行位置
 * @param {Object} worksheet - Excel工作表对象
 * @returns {Object} 表头信息
 */
function analyzeWorksheetStructure(worksheet) {
  let headerRow1Index = -1;
  let headerRow2Index = -1;
  let dataStartRow = -1;

  // 查找表头行（通常包含"成本目标+成本组成"等关键字）
  for (let i = 1; i <= Math.min(10, worksheet.rowCount); i++) {
    const row = worksheet.getRow(i);
    let rowText = "";

    row.eachCell({ includeEmpty: false }, (cell) => {
      rowText += cell.value ? cell.value.toString() : "";
    });

    if (rowText.includes("成本目标") || rowText.includes("成本组成")) {
      if (headerRow1Index === -1) {
        headerRow1Index = i;
        console.log(
          `  \x1b[36m[分析] \x1b[0m第一行表头位于第 \x1b[32m${i}\x1b[0m 行`
        );
      } else if (headerRow2Index === -1) {
        headerRow2Index = i;
        console.log(
          `  \x1b[36m[分析] \x1b[0m第二行表头位于第 \x1b[32m${i}\x1b[0m 行`
        );
      }
    }
  }

  // 如果没找到表头，使用默认值
  if (headerRow1Index === -1) {
    headerRow1Index = 1;
    headerRow2Index = 2;
    console.log(`  \x1b[33m[警告] \x1b[0m未找到表头，使用默认位置`);
  } else if (headerRow2Index === -1) {
    headerRow2Index = headerRow1Index + 1;
  }

  // 数据开始行通常在第二个表头行之后
  dataStartRow = headerRow2Index + 1;

  return {
    headerRow1Index,
    headerRow2Index,
    dataStartRow,
  };
}

/**
 * 处理工作表数据，提取成本项
 * @param {Object} worksheet - Excel工作表对象
 * @returns {Array} 成本项数组
 */
function processCostCalculationWorksheet(worksheet) {
  console.log(
    `\n\x1b[36m[处理] \x1b[0m开始处理工作表: \x1b[32m${worksheet.name}\x1b[0m`
  );

  // 分析工作表结构
  const structure = analyzeWorksheetStructure(worksheet);
  const { headerRow1Index, headerRow2Index, dataStartRow } = structure;

  // 获取表头行
  const headerRow1 = worksheet.getRow(headerRow1Index);
  const headerRow2 = worksheet.getRow(headerRow2Index);

  // 获取列映射
  const columnMapping = getCostCalculationColumnMapping(headerRow1, headerRow2);
  applyDefaultCostCalculationMapping(columnMapping);

  console.log(`  \x1b[36m[信息] \x1b[0m列映射:`, columnMapping);
  console.log(
    `  \x1b[36m[信息] \x1b[0m数据起始行: \x1b[32m${dataStartRow}\x1b[0m`
  );

  const costItems = [];
  let currentDivision = "";
  let processedCount = 0;

  // 遍历数据行
  for (let rowNum = dataStartRow; rowNum <= worksheet.rowCount; rowNum++) {
    const row = worksheet.getRow(rowNum);

    // 检查是否为空行
    let isEmpty = true;
    row.eachCell({ includeEmpty: false }, () => {
      isEmpty = false;
    });

    if (isEmpty) continue;

    // 提取行数据
    const rowData = extractRowData(row, columnMapping);

    // 跳过没有有效数据的行
    if (!rowData.costType && !rowData.feature) continue;

    // 创建成本项对象
    const costItem = createCostItem(rowData, rowNum, currentDivision);

    // 判断是否为分部工程行
    if (isDivisionRow(costItem)) {
      currentDivision = costItem.itemName;
      console.log(
        `  \x1b[36m[发现] \x1b[0m分部工程: \x1b[33m${currentDivision}\x1b[0m`
      );
      continue;
    }

    // 添加有效的成本项
    if (isValidCostItem(costItem) || costItem.costType === "目标") {
      costItems.push(costItem);
      processedCount++;

      // 每处理50个项目显示一次进度
      if (processedCount % 50 === 0) {
        console.log(
          `  \x1b[36m[进度] \x1b[0m已处理 \x1b[32m${processedCount}\x1b[0m 个成本项`
        );
      }
    }
  }

  console.log(
    `  \x1b[36m[完成] \x1b[0m共处理 \x1b[32m${processedCount}\x1b[0m 个成本项`
  );
  return costItems;
}

/**
 * 从行中提取数据
 * @param {Object} row - Excel行对象
 * @param {Object} columnMapping - 列映射对象
 * @returns {Object} 行数据对象
 */
function extractRowData(row, columnMapping) {
  return {
    costType: row.getCell(columnMapping.costType).value,
    costComponent: row.getCell(columnMapping.costComponent).value,
    feature: row.getCell(columnMapping.feature).value,
    unit: row.getCell(columnMapping.unit).value,
    totalQuantity: row.getCell(columnMapping.totalQuantity).value,
    content: row.getCell(columnMapping.content).value,
    calculatedQuantity: row.getCell(columnMapping.calculatedQuantity).value,
    unitPrice: row.getCell(columnMapping.unitPrice).value,
    totalCost: row.getCell(columnMapping.totalCost).value,
    laborCost: row.getCell(columnMapping.laborCost).value,
    materialCost: row.getCell(columnMapping.materialCost).value,
    subcontractCost: row.getCell(columnMapping.subcontractCost).value,
    comprehensiveUnitPrice: row.getCell(columnMapping.comprehensiveUnitPrice)
      .value,
    taxRate: row.getCell(columnMapping.taxRate).value,
  };
}

/**
 * 验证处理结果
 * @param {Array} costItems - 成本项数组
 * @returns {Object} 验证结果
 */
function validateProcessingResult(costItems) {
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
    statistics: {
      totalItems: costItems.length,
      targetItems: 0,
      materialItems: 0,
      laborItems: 0,
      subcontractItems: 0,
    },
  };

  // 统计各类型数量
  costItems.forEach((item) => {
    switch (item.costType) {
      case "目标":
        validation.statistics.targetItems++;
        break;
      case "材":
        validation.statistics.materialItems++;
        break;
      case "人":
        validation.statistics.laborItems++;
        break;
      case "专":
        validation.statistics.subcontractItems++;
        break;
    }
  });

  // 基本验证
  if (costItems.length === 0) {
    validation.isValid = false;
    validation.errors.push("未找到任何有效的成本项数据");
  }

  if (validation.statistics.targetItems === 0) {
    validation.warnings.push("未找到目标项数据");
  }

  return validation;
}

module.exports = {
  findTargetWorksheet,
  analyzeWorksheetStructure,
  processCostCalculationWorksheet,
  extractRowData,
  validateProcessingResult,
};
