/**
 * 清单测算系统工具函数
 */

/**
 * 安全转换字符串
 * @param {*} value - 原始值
 * @returns {string} 转换后的字符串
 */
function safeParseString(value) {
  if (!value) return "";

  // 处理复杂对象类型
  if (typeof value === "object" && value.richText) {
    // 处理富文本格式
    return value.richText
      .map((rt) => rt.text || "")
      .join("")
      .trim();
  } else if (typeof value === "object" && value.result) {
    // 处理公式结果
    return value.result.toString().trim();
  } else {
    return value.toString().trim();
  }
}

/**
 * 安全转换数值
 * @param {*} value - 原始值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的数值
 */
function safeParseNumber(value, defaultValue = 0) {
  if (value === null || value === undefined || value === "") {
    return defaultValue;
  }

  const num = parseFloat(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 提取钢筋直径数字
 * @param {string} steelName - 钢筋名称
 * @returns {number} 直径数字
 */
function extractSteelNumber(steelName) {
  const match = steelName.match(/Φ(\d+)/);
  return match ? parseInt(match[1]) : 999; // 未匹配的排在最后
}

/**
 * 提取混凝土标号
 * @param {string} concreteName - 混凝土名称
 * @returns {number} 标号数字
 */
function extractConcreteGrade(concreteName) {
  const match = concreteName.match(/C(\d+)/);
  return match ? parseInt(match[1]) : 30; // 默认C30
}

/**
 * 提取混凝土附加费关键词
 * @param {string} concreteName - 混凝土名称
 * @returns {Array} 附加费关键词数组
 */
function extractAdditives(concreteName) {
  if (!concreteName || typeof concreteName !== "string") {
    return [];
  }

  const additives = [];
  const keywords = ["细石", "泵送", "抗渗", "抗冻", "早强", "缓凝", "膨胀"];

  keywords.forEach((keyword) => {
    if (concreteName.includes(keyword)) {
      additives.push(keyword);
    }
  });

  return additives;
}

/**
 * 判断是否为钢筋材料
 * @param {string} itemName - 项目名称
 * @returns {boolean} 是否为钢筋
 */
function isSteelMaterial(itemName) {
  if (!itemName || typeof itemName !== "string") return false;
  return itemName.includes("钢筋") || itemName.includes("Φ");
}

/**
 * 判断是否为混凝土材料
 * @param {string} itemName - 项目名称
 * @returns {boolean} 是否为混凝土
 */
function isConcreteMaterial(itemName) {
  if (!itemName || typeof itemName !== "string") return false;
  return itemName.includes("混凝土") || itemName.includes("商品混凝土");
}

/**
 * 判断是否为砂浆材料
 * @param {string} itemName - 项目名称
 * @returns {boolean} 是否为砂浆
 */
function isMortarMaterial(itemName) {
  if (!itemName || typeof itemName !== "string") return false;
  return itemName.includes("砂浆") || itemName.includes("预拌砂浆");
}

/**
 * 判断是否为砖类材料
 * @param {string} itemName - 项目名称
 * @returns {boolean} 是否为砖类
 */
function isBrickMaterial(itemName) {
  if (!itemName || typeof itemName !== "string") return false;
  return itemName.includes("砖") || itemName.includes("砌块");
}

/**
 * 获取材料分类
 * @param {string} itemName - 项目名称
 * @returns {string} 材料分类
 */
function getMaterialCategory(itemName) {
  if (isSteelMaterial(itemName)) return "钢筋类";
  if (isConcreteMaterial(itemName)) return "混凝土类";
  if (isMortarMaterial(itemName)) return "砂浆类";
  if (isBrickMaterial(itemName)) return "砖类";
  return "其他";
}

/**
 * 生成Excel列名
 * @param {number} columnIndex - 列索引（从1开始）
 * @returns {string} Excel列名（A, B, C, ..., AA, AB, ...）
 */
function getExcelColumnName(columnIndex) {
  let result = "";
  while (columnIndex > 0) {
    columnIndex--;
    result = String.fromCharCode(65 + (columnIndex % 26)) + result;
    columnIndex = Math.floor(columnIndex / 26);
  }
  return result;
}

/**
 * 生成Excel单元格地址
 * @param {number} row - 行号
 * @param {number} column - 列号
 * @returns {string} 单元格地址（如A1, B2）
 */
function getExcelCellAddress(row, column) {
  return `${getExcelColumnName(column)}${row}`;
}

/**
 * 验证数据有效性
 * @param {Object} data - 数据对象
 * @param {Array} requiredFields - 必填字段数组
 * @returns {Object} 验证结果
 */
function validateData(data, requiredFields = []) {
  const errors = [];
  const warnings = [];

  // 检查必填字段
  requiredFields.forEach((field) => {
    if (!data[field] || data[field] === "") {
      errors.push(`字段 ${field} 不能为空`);
    }
  });

  // 检查数值字段
  const numericFields = ["quantity", "content", "unitPrice"];
  numericFields.forEach((field) => {
    if (data[field] !== undefined && isNaN(parseFloat(data[field]))) {
      errors.push(`字段 ${field} 必须为数值`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 格式化金额
 * @param {number} amount - 金额
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的金额
 */
function formatAmount(amount, decimals = 2) {
  if (isNaN(amount)) return "0.00";
  return amount.toFixed(decimals);
}

/**
 * 格式化数量
 * @param {number} quantity - 数量
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的数量
 */
function formatQuantity(quantity, decimals = 2) {
  if (isNaN(quantity)) return "0.00";
  return quantity.toFixed(decimals);
}

/**
 * 深度克隆对象
 * @param {Object} obj - 要克隆的对象
 * @returns {Object} 克隆后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== "object") return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map((item) => deepClone(item));

  const cloned = {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
}

/**
 * 按分部工程分组
 * @param {Array} items - 项目数组
 * @returns {Map} 分组结果
 */
function groupByDivision(items) {
  const groups = new Map();

  items.forEach((item) => {
    const division = item.division || "未分类";
    if (!groups.has(division)) {
      groups.set(division, []);
    }
    groups.get(division).push(item);
  });

  return groups;
}

/**
 * 按名称和单位分组
 * @param {Array} items - 项目数组
 * @returns {Map} 分组结果
 */
function groupByNameAndUnit(items) {
  const groups = new Map();

  items.forEach((item) => {
    const key = `${item.itemName}_${item.unit}`;
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(item);
  });

  return groups;
}

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀
 * @returns {string} 唯一ID
 */
function generateId(prefix = "id") {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 等待用户输入后退出
 */
function waitForUserExit() {
  console.log("\x1b[33m按回车键退出...\x1b[0m");

  // 检查是否在TTY环境下
  if (process.stdin.isTTY) {
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on("data", process.exit.bind(process, 0));
  } else {
    // 非TTY环境下，直接退出
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  }
}

module.exports = {
  safeParseString,
  safeParseNumber,
  extractSteelNumber,
  extractConcreteGrade,
  extractAdditives,
  isSteelMaterial,
  isConcreteMaterial,
  isMortarMaterial,
  isBrickMaterial,
  getMaterialCategory,
  getExcelColumnName,
  getExcelCellAddress,
  validateData,
  formatAmount,
  formatQuantity,
  deepClone,
  groupByDivision,
  groupByNameAndUnit,
  generateId,
  waitForUserExit,
};
