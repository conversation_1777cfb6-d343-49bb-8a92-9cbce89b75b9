const { CalculatedData, CostType } = require("./types");
const { BusinessRules } = require("./config");
const { safeParseNumber, groupByDivision } = require("./utils");

/**
 * 分部分项成本测算更新器
 * 实现H~Q列的自动计算和Excel公式生成
 */

/**
 * 查找父级目标项
 * @param {Object} currentItem - 当前项目
 * @param {Array} allItems - 所有项目数组
 * @returns {Object|null} 父级目标项
 */
function findParentTargetItem(currentItem, allItems) {
  // 在同一分部工程中，找到当前项目之前最近的目标项
  const sameDivisionItems = allItems.filter(
    (item) =>
      item.division === currentItem.division &&
      item.rowIndex < currentItem.rowIndex
  );

  // 按行号倒序排列，找到最近的目标项
  const sortedItems = sameDivisionItems.sort((a, b) => b.rowIndex - a.rowIndex);

  for (let item of sortedItems) {
    if (item.costType === CostType.TARGET) {
      return item;
    }
  }

  return null;
}

/**
 * 生成测算成本量公式（H列）
 * @param {Object} currentItem - 当前项目
 * @param {Object} parentTarget - 父级目标项
 * @returns {string} Excel公式
 */
function generateCalculatedQuantityFormula(currentItem, parentTarget) {
  if (!parentTarget) {
    // 如果没有父级目标项，使用自身的工程量
    return `=F${currentItem.rowIndex}`;
  }

  // 测算成本量 = 父级目标工程量 × 本级含量
  return `=F${parentTarget.rowIndex}*G${currentItem.rowIndex}`;
}

/**
 * 生成测算单价公式（I列）
 * @param {Object} currentItem - 当前项目
 * @param {string} sheetName - 引用的工作表名称
 * @returns {string} Excel公式
 */
function generateUnitPriceFormula(currentItem, sheetName) {
  const itemName = currentItem.itemName;

  switch (currentItem.costType) {
    case CostType.MATERIAL:
      // 从材料清单引用单价
      return `=IFERROR(INDEX('4-材料清单'.H:H,MATCH("${itemName}",'4-材料清单'.C:C,0)),0)`;

    case CostType.LABOR:
      // 从劳务清单引用单价
      return `=IFERROR(INDEX('4-劳务清单'.H:H,MATCH("${itemName}",'4-劳务清单'.C:C,0)),0)`;

    case CostType.SUBCONTRACT:
      // 从专业分包清单引用单价
      return `=IFERROR(INDEX('4-专业分包清单'.H:H,MATCH("${itemName}",'4-专业分包清单'.C:C,0)),0)`;

    case CostType.TARGET:
      // 目标项不需要单价
      return "0";

    default:
      return "0";
  }
}

/**
 * 生成测算成本合计公式（J列）
 * @param {number} rowIndex - 行索引
 * @returns {string} Excel公式
 */
function generateTotalCostFormula(rowIndex) {
  // 测算成本合计 = 劳务费 + 材料费 + 专业分包
  return `=K${rowIndex}+L${rowIndex}+M${rowIndex}`;
}

/**
 * 生成成本分配公式（K/L/M列）
 * @param {Object} currentItem - 当前项目
 * @param {number} rowIndex - 行索引
 * @returns {Object} 包含K/L/M列公式的对象
 */
function generateCostAllocationFormulas(currentItem, rowIndex) {
  let formulas = {
    laborCost: "0", // K列：劳务费
    materialCost: "0", // L列：材料费
    subcontractCost: "0", // M列：专业分包
  };

  // 测算金额 = 测算成本量 × 测算单价
  const amountFormula = `=H${rowIndex}*I${rowIndex}`;

  switch (currentItem.costType) {
    case CostType.LABOR:
      formulas.laborCost = amountFormula;
      break;
    case CostType.MATERIAL:
      formulas.materialCost = amountFormula;
      break;
    case CostType.SUBCONTRACT:
      formulas.subcontractCost = amountFormula;
      break;
    case CostType.TARGET:
      // 目标项的成本分配需要汇总其下属项目
      formulas = generateTargetCostAllocationFormulas(currentItem, rowIndex);
      break;
  }

  return formulas;
}

/**
 * 生成目标项成本分配公式
 * @param {Object} targetItem - 目标项
 * @param {number} rowIndex - 行索引
 * @returns {Object} 包含K/L/M列公式的对象
 */
function generateTargetCostAllocationFormulas(targetItem, rowIndex) {
  // 目标项的成本分配需要根据其下属项目的范围来计算
  // 这里简化处理，实际应该根据下属项目的行号范围来生成SUM公式

  return {
    laborCost: `=SUMIF(B:B,"人",K:K)`, // 汇总所有劳务费
    materialCost: `=SUMIF(B:B,"材",L:L)`, // 汇总所有材料费
    subcontractCost: `=SUMIF(B:B,"专",M:M)`, // 汇总所有专业分包费
  };
}

/**
 * 生成测算综合单价公式（N列）
 * @param {Object} currentItem - 当前项目
 * @param {number} rowIndex - 行索引
 * @returns {string} Excel公式
 */
function generateComprehensiveUnitPriceFormula(currentItem, rowIndex) {
  if (currentItem.costType === CostType.TARGET) {
    // 测算综合单价 = 测算成本合计 ÷ 合并后清单工程量
    return `=IF(F${rowIndex}=0,0,J${rowIndex}/F${rowIndex})`;
  } else {
    // 非目标项不需要综合单价
    return "0";
  }
}

/**
 * 生成税率相关公式（O/P列）
 * @param {Object} currentItem - 当前项目
 * @param {number} rowIndex - 行索引
 * @returns {Object} 包含税率和进项税额公式的对象
 */
function generateTaxFormulas(currentItem, rowIndex) {
  let taxRate = "0";
  let taxAmount = "0";

  switch (currentItem.costType) {
    case CostType.MATERIAL:
      taxRate = "0.13"; // 材料税率13%
      taxAmount = `=L${rowIndex}*O${rowIndex}/(1+O${rowIndex})`;
      break;
    case CostType.LABOR:
      taxRate = "0.03"; // 劳务税率3%
      taxAmount = `=K${rowIndex}*O${rowIndex}/(1+O${rowIndex})`;
      break;
    case CostType.SUBCONTRACT:
      taxRate = "0.09"; // 专业分包税率9%
      taxAmount = `=M${rowIndex}*O${rowIndex}/(1+O${rowIndex})`;
      break;
    case CostType.TARGET:
      taxRate = "0";
      taxAmount = `=K${rowIndex}*0.03/(1+0.03)+L${rowIndex}*0.13/(1+0.13)+M${rowIndex}*0.09/(1+0.09)`;
      break;
  }

  return {
    taxRate,
    taxAmount,
  };
}

/**
 * 计算单个项目的成本数据
 * @param {Object} inputItem - 输入项目数据
 * @param {Array} allItems - 所有项目数组
 * @returns {CalculatedData} 计算结果数据
 */
function calculateSingleItem(inputItem, allItems) {
  const calculatedData = new CalculatedData(inputItem);
  const rowIndex = inputItem.rowIndex;

  // 1. 查找父级目标项
  const parentTarget = findParentTargetItem(inputItem, allItems);

  // 2. 生成测算成本量公式（H列）
  const quantityFormula = generateCalculatedQuantityFormula(
    inputItem,
    parentTarget
  );
  calculatedData.setFormula("calculatedQuantity", quantityFormula);

  // 3. 生成测算单价公式（I列）
  const unitPriceFormula = generateUnitPriceFormula(inputItem);
  calculatedData.setFormula("unitPrice", unitPriceFormula);

  // 4. 生成测算成本合计公式（J列）
  const totalCostFormula = generateTotalCostFormula(rowIndex);
  calculatedData.setFormula("totalCost", totalCostFormula);

  // 5. 生成成本分配公式（K/L/M列）
  const costAllocationFormulas = generateCostAllocationFormulas(
    inputItem,
    rowIndex
  );
  calculatedData.setFormula("laborCost", costAllocationFormulas.laborCost);
  calculatedData.setFormula(
    "materialCost",
    costAllocationFormulas.materialCost
  );
  calculatedData.setFormula(
    "subcontractCost",
    costAllocationFormulas.subcontractCost
  );

  // 6. 生成测算综合单价公式（N列）
  const comprehensiveUnitPriceFormula = generateComprehensiveUnitPriceFormula(
    inputItem,
    rowIndex
  );
  calculatedData.setFormula(
    "comprehensiveUnitPrice",
    comprehensiveUnitPriceFormula
  );

  // 7. 生成税率相关公式（O/P列）
  const taxFormulas = generateTaxFormulas(inputItem, rowIndex);
  calculatedData.taxRate = parseFloat(taxFormulas.taxRate);
  calculatedData.setFormula("taxAmount", taxFormulas.taxAmount);

  return calculatedData;
}

/**
 * 更新分部分项成本测算表
 * @param {Array} userInputData - 用户输入数据数组
 * @param {Object} options - 选项
 * @returns {Object} 更新结果
 */
function updateCostCalculationSheet(userInputData, options = {}) {
  console.log(
    `\n\x1b[36m[成本测算更新] \x1b[0m开始更新分部分项成本测算表，共 \x1b[32m${userInputData.length}\x1b[0m 个项目`
  );

  try {
    const calculatedResults = [];
    let processedCount = 0;

    // 逐个处理每个项目
    userInputData.forEach((inputItem) => {
      const calculatedData = calculateSingleItem(inputItem, userInputData);
      calculatedResults.push(calculatedData);
      processedCount++;

      // 每处理20个项目显示一次进度
      if (processedCount % 20 === 0) {
        console.log(
          `  \x1b[36m[进度] \x1b[0m已处理 \x1b[32m${processedCount}\x1b[0m 个项目`
        );
      }
    });

    // 生成统计信息
    const statistics = {
      totalItems: calculatedResults.length,
      targetItems: calculatedResults.filter(
        (item) => item.costType === CostType.TARGET
      ).length,
      materialItems: calculatedResults.filter(
        (item) => item.costType === CostType.MATERIAL
      ).length,
      laborItems: calculatedResults.filter(
        (item) => item.costType === CostType.LABOR
      ).length,
      subcontractItems: calculatedResults.filter(
        (item) => item.costType === CostType.SUBCONTRACT
      ).length,
    };

    console.log(
      `\x1b[36m[成本测算更新] \x1b[0m更新完成，共处理 \x1b[32m${calculatedResults.length}\x1b[0m 个项目`
    );
    console.log(
      `  \x1b[36m[统计] \x1b[0m目标项: \x1b[32m${statistics.targetItems}\x1b[0m, 材料项: \x1b[32m${statistics.materialItems}\x1b[0m, 劳务项: \x1b[32m${statistics.laborItems}\x1b[0m, 专业分包项: \x1b[32m${statistics.subcontractItems}\x1b[0m`
    );

    return {
      success: true,
      calculatedResults,
      statistics,
    };
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m成本测算更新失败: ${error.message}`);
    console.error(`\x1b[31m[错误堆栈] \x1b[0m${error.stack}`);
    return {
      success: false,
      error: error.message,
      calculatedResults: [],
    };
  }
}

module.exports = {
  findParentTargetItem,
  generateCalculatedQuantityFormula,
  generateUnitPriceFormula,
  generateTotalCostFormula,
  generateCostAllocationFormulas,
  generateTargetCostAllocationFormulas,
  generateComprehensiveUnitPriceFormula,
  generateTaxFormulas,
  calculateSingleItem,
  updateCostCalculationSheet,
};
