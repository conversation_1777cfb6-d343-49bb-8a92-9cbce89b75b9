/**
 * 清单测算系统核心数据结构定义
 */

/**
 * 成本类型枚举
 */
const CostType = {
  TARGET: "目标",
  LABOR: "人", 
  MATERIAL: "材",
  SUBCONTRACT: "专"
};

/**
 * 材料分类枚举
 */
const MaterialCategory = {
  STEEL: "钢筋类",
  CONCRETE: "混凝土类",
  MORTAR: "砂浆类", 
  BRICK: "砖类",
  OTHER: "其他"
};

/**
 * 用户输入数据结构（A~F列）
 */
class UserInputData {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.rowIndex = data.rowIndex || 0;
    this.division = data.division || "";           // A列：分部工程
    this.costType = data.costType || "";           // B列：成本类型
    this.itemName = data.itemName || "";           // C列：项目名称
    this.feature = data.feature || "";             // D列：项目特征
    this.unit = data.unit || "";                   // E列：计量单位
    this.quantity = data.quantity || 0;            // F列：合并后清单工程量
    this.content = data.content || 0;              // G列：含量
  }

  generateId() {
    return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  isValid() {
    return this.costType && this.itemName && this.unit;
  }

  isTarget() {
    return this.costType === CostType.TARGET;
  }

  isMaterial() {
    return this.costType === CostType.MATERIAL;
  }

  isLabor() {
    return this.costType === CostType.LABOR;
  }

  isSubcontract() {
    return this.costType === CostType.SUBCONTRACT;
  }
}

/**
 * 计算结果数据结构（H~Q列）
 */
class CalculatedData extends UserInputData {
  constructor(data = {}) {
    super(data);
    this.calculatedQuantity = data.calculatedQuantity || 0;      // H列：测算成本量
    this.unitPrice = data.unitPrice || 0;                       // I列：测算单价
    this.totalCost = data.totalCost || 0;                       // J列：测算成本合计
    this.laborCost = data.laborCost || 0;                       // K列：劳务费
    this.materialCost = data.materialCost || 0;                 // L列：材料费
    this.subcontractCost = data.subcontractCost || 0;           // M列：专业分包
    this.comprehensiveUnitPrice = data.comprehensiveUnitPrice || 0; // N列：测算综合单价
    this.taxRate = data.taxRate || 0;                           // O列：税率
    this.taxAmount = data.taxAmount || 0;                       // P列：进项税额
    this.remarks = data.remarks || "";                          // Q列：备注
    
    // Excel公式存储
    this.formulas = {
      calculatedQuantity: "",
      unitPrice: "",
      totalCost: "",
      laborCost: "",
      materialCost: "",
      subcontractCost: "",
      comprehensiveUnitPrice: "",
      taxAmount: ""
    };
  }

  setFormula(field, formula) {
    if (this.formulas.hasOwnProperty(field)) {
      this.formulas[field] = formula;
    }
  }

  getFormula(field) {
    return this.formulas[field] || "";
  }
}

/**
 * 材料清单项数据结构
 */
class MaterialListItem {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.division = data.division || "";           // 分部工程
    this.originalItem = data.originalItem || "";   // 原测算清单
    this.specification = data.specification || ""; // 规格型号
    this.unit = data.unit || "";                   // 计量单位
    this.content = data.content || 0;              // 测算含量
    this.quantity = data.quantity || 0;            // 测算成本量
    this.unitPrice = data.unitPrice || 0;          // 测算单价
    this.amount = data.amount || 0;                // 测算金额
    this.taxRate = data.taxRate || 0;              // 税率
    this.category = data.category || MaterialCategory.OTHER; // 材料分类
    this.sortOrder = data.sortOrder || 0;          // 排序序号
    this.isParent = data.isParent || false;        // 是否为父级行
    this.parentId = data.parentId || "";           // 父级ID
    this.level = data.level || 1;                  // 层级
    
    // Excel公式
    this.formulas = {
      content: "",
      quantity: "",
      amount: ""
    };
  }

  generateId() {
    return `material_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  setFormula(field, formula) {
    if (this.formulas.hasOwnProperty(field)) {
      this.formulas[field] = formula;
    }
  }
}

/**
 * 劳务清单项数据结构
 */
class LaborListItem {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.serialNo = data.serialNo || "";           // 序号
    this.division = data.division || "";           // 分部工程
    this.originalItem = data.originalItem || "";   // 原测算清单
    this.workContent = data.workContent || "";     // 工作内容
    this.unit = data.unit || "";                   // 计量单位
    this.content = data.content || 0;              // 测算含量
    this.quantity = data.quantity || 0;            // 测算成本量
    this.unitPrice = data.unitPrice || 0;          // 测算单价
    this.amount = data.amount || 0;                // 测算金额
    this.taxRate = data.taxRate || 0;              // 税率
    this.type = data.type || "item";               // 类型：division/merged/item
    this.parentId = data.parentId || "";           // 父级ID
    this.level = data.level || 1;                  // 层级
    
    // Excel公式
    this.formulas = {
      content: "",
      quantity: "",
      amount: ""
    };
  }

  generateId() {
    return `labor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  setFormula(field, formula) {
    if (this.formulas.hasOwnProperty(field)) {
      this.formulas[field] = formula;
    }
  }

  isDivisionTotal() {
    return this.type === "division";
  }

  isMergedItem() {
    return this.type === "merged";
  }
}

/**
 * 专业分包清单项数据结构（与劳务清单结构相同）
 */
class SubcontractListItem extends LaborListItem {
  constructor(data = {}) {
    super(data);
  }

  generateId() {
    return `subcontract_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 项目汇总信息
 */
class ProjectSummary {
  constructor(data = {}) {
    this.projectName = data.projectName || "";
    this.fileName = data.fileName || "";
    this.processTime = data.processTime || new Date();
    this.totalItems = data.totalItems || 0;
    this.targetItems = data.targetItems || 0;
    this.materialItems = data.materialItems || 0;
    this.laborItems = data.laborItems || 0;
    this.subcontractItems = data.subcontractItems || 0;
    this.divisions = data.divisions || [];
    this.totalAmount = data.totalAmount || 0;
    this.materialAmount = data.materialAmount || 0;
    this.laborAmount = data.laborAmount || 0;
    this.subcontractAmount = data.subcontractAmount || 0;
  }
}

/**
 * 处理结果数据结构
 */
class ProcessResult {
  constructor(data = {}) {
    this.success = data.success || false;
    this.message = data.message || "";
    this.error = data.error || null;
    this.inputData = data.inputData || [];
    this.calculatedData = data.calculatedData || [];
    this.materialList = data.materialList || [];
    this.laborList = data.laborList || [];
    this.subcontractList = data.subcontractList || [];
    this.summary = data.summary || new ProjectSummary();
    this.outputPath = data.outputPath || "";
  }
}

module.exports = {
  CostType,
  MaterialCategory,
  UserInputData,
  CalculatedData,
  MaterialListItem,
  LaborListItem,
  SubcontractListItem,
  ProjectSummary,
  ProcessResult
};
