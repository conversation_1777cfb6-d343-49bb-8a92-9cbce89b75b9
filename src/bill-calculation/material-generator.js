const { MaterialListItem, CostType } = require("./types");
const { BusinessRules } = require("./config");
const { 
  getMaterialCategory,
  extractSteelNumber,
  extractConcreteGrade,
  extractAdditives,
  generateId
} = require("./utils");

/**
 * 材料清单生成器
 * 实现材料清单的智能排序、混凝土分组和定价算法
 */

/**
 * 创建混凝土父级分组
 * @param {Array} concreteItems - 混凝土项目数组
 * @returns {Object} 包含父级行和子级行的对象
 */
function createConcreteGroup(concreteItems) {
  if (concreteItems.length === 0) {
    return { parentItem: null, childItems: [] };
  }
  
  // 创建父级行
  const parentItem = new MaterialListItem({
    division: "混凝土工程",
    originalItem: "商品混凝土",
    specification: "商品混凝土",
    unit: "",
    isParent: true,
    level: 1,
    category: "混凝土类"
  });
  
  // 设置子级行的父级关系
  const childItems = concreteItems.map((item, index) => {
    const childItem = new MaterialListItem({
      ...item,
      parentId: parentItem.id,
      level: 2,
      sortOrder: index + 1
    });
    
    // 设置规格型号（从项目名称提取）
    childItem.specification = extractConcreteSpecification(item.itemName);
    
    return childItem;
  });
  
  console.log(`  \x1b[36m[混凝土分组] \x1b[0m创建父级分组，包含 \x1b[32m${childItems.length}\x1b[0m 个子项`);
  
  return { parentItem, childItems };
}

/**
 * 提取混凝土规格型号
 * @param {string} itemName - 项目名称
 * @returns {string} 规格型号
 */
function extractConcreteSpecification(itemName) {
  // 提取标号
  const grade = extractConcreteGrade(itemName);
  const additives = extractAdditives(itemName);
  
  let spec = `C${grade}`;
  
  // 添加附加特性
  if (additives.length > 0) {
    spec += ` ${additives.join('+')}`;
  }
  
  return spec;
}

/**
 * 计算混凝土价格
 * @param {string} itemName - 混凝土名称
 * @param {number} basePrice - C30基准价格
 * @returns {number} 计算后的价格
 */
function calculateConcretePrice(itemName, basePrice = 300) {
  const grade = extractConcreteGrade(itemName);
  const additives = extractAdditives(itemName);
  
  let price = basePrice; // C30基准价
  
  // 标号差价
  const gradePricing = BusinessRules.concretePricing.gradePricing;
  const gradeKey = grade.toString();
  if (gradePricing[gradeKey] !== undefined) {
    price += gradePricing[gradeKey];
  }
  
  // 附加费
  const additiveDict = BusinessRules.concretePricing.additives;
  additives.forEach(additive => {
    if (additiveDict[additive]) {
      price += additiveDict[additive];
    }
  });
  
  // 泵送费规则：C15/C20/C25不含泵送时减10
  if (grade <= BusinessRules.concretePricing.pumpingRules.lowGradeThreshold && 
      !additives.includes('泵送')) {
    price -= BusinessRules.concretePricing.pumpingRules.lowGradeReduction;
  }
  
  return price;
}

/**
 * 智能排序材料
 * @param {Array} materials - 材料数组
 * @returns {Array} 排序后的材料数组
 */
function smartSortMaterials(materials) {
  console.log(`  \x1b[36m[材料排序] \x1b[0m开始智能排序，共 \x1b[32m${materials.length}\x1b[0m 个材料`);
  
  // 1. 按类别分组
  const categorized = {
    steel: [],
    concrete: [],
    mortar: [],
    brick: [],
    others: []
  };
  
  materials.forEach(material => {
    const category = getMaterialCategory(material.itemName);
    switch (category) {
      case "钢筋类":
        categorized.steel.push(material);
        break;
      case "混凝土类":
        categorized.concrete.push(material);
        break;
      case "砂浆类":
        categorized.mortar.push(material);
        break;
      case "砖类":
        categorized.brick.push(material);
        break;
      default:
        categorized.others.push(material);
        break;
    }
  });
  
  // 2. 钢筋类按直径数字排序
  categorized.steel.sort((a, b) => {
    const numA = extractSteelNumber(a.itemName);
    const numB = extractSteelNumber(b.itemName);
    return numA - numB;
  });
  
  // 3. 混凝土类按标号排序
  categorized.concrete.sort((a, b) => {
    const gradeA = extractConcreteGrade(a.itemName);
    const gradeB = extractConcreteGrade(b.itemName);
    return gradeA - gradeB;
  });
  
  // 4. 处理混凝土分组
  const concreteGroup = createConcreteGroup(categorized.concrete);
  const concreteResult = concreteGroup.parentItem ? 
    [concreteGroup.parentItem, ...concreteGroup.childItems] : 
    concreteGroup.childItems;
  
  // 5. 组合最终结果
  const sortedMaterials = [
    ...categorized.steel,
    ...concreteResult,
    ...categorized.mortar,
    ...categorized.brick,
    ...categorized.others
  ];
  
  console.log(`  \x1b[36m[材料排序] \x1b[0m排序完成: 钢筋 \x1b[32m${categorized.steel.length}\x1b[0m, 混凝土 \x1b[32m${concreteResult.length}\x1b[0m, 砂浆 \x1b[32m${categorized.mortar.length}\x1b[0m, 砖 \x1b[32m${categorized.brick.length}\x1b[0m, 其他 \x1b[32m${categorized.others.length}\x1b[0m`);
  
  return sortedMaterials;
}

/**
 * 生成材料清单项
 * @param {Object} materialData - 材料数据
 * @param {number} serialNo - 序号
 * @returns {MaterialListItem} 材料清单项
 */
function generateMaterialListItem(materialData, serialNo) {
  const item = new MaterialListItem({
    division: materialData.division,
    originalItem: materialData.itemName,
    specification: materialData.itemName,
    unit: materialData.unit,
    content: materialData.content,
    quantity: materialData.calculatedQuantity,
    unitPrice: materialData.unitPrice,
    amount: materialData.totalCost,
    taxRate: materialData.taxRate || 0.13,
    category: getMaterialCategory(materialData.itemName),
    sortOrder: serialNo
  });
  
  // 设置Excel公式
  item.setFormula('content', `='3-分部分项成本测算'.G${materialData.rowIndex}`);
  item.setFormula('quantity', `='3-分部分项成本测算'.H${materialData.rowIndex}`);
  item.setFormula('amount', `=G${serialNo + 2}*H${serialNo + 2}`); // +2是因为表头占2行
  
  return item;
}

/**
 * 应用混凝土定价
 * @param {Array} materialList - 材料清单数组
 * @param {number} basePrice - C30基准价格
 */
function applyConcretepricing(materialList, basePrice = 300) {
  console.log(`  \x1b[36m[混凝土定价] \x1b[0m开始应用定价算法，基准价格: \x1b[32m${basePrice}\x1b[0m`);
  
  let pricedCount = 0;
  
  materialList.forEach(item => {
    if (item.category === "混凝土类" && !item.isParent) {
      const calculatedPrice = calculateConcretePrice(item.originalItem, basePrice);
      item.unitPrice = calculatedPrice;
      
      // 重新计算金额
      item.amount = item.quantity * item.unitPrice;
      
      pricedCount++;
    }
  });
  
  console.log(`  \x1b[36m[混凝土定价] \x1b[0m完成 \x1b[32m${pricedCount}\x1b[0m 个混凝土项目的定价`);
}

/**
 * 生成材料清单
 * @param {Array} materialData - 材料数据数组
 * @param {Object} options - 选项
 * @returns {Array} 材料清单数组
 */
function generateMaterialList(materialData, options = {}) {
  console.log(`\n\x1b[36m[材料清单生成] \x1b[0m开始生成材料清单，共 \x1b[32m${materialData.length}\x1b[0m 个材料`);
  
  try {
    // 1. 过滤材料类型数据
    const materials = materialData.filter(item => item.costType === CostType.MATERIAL);
    
    if (materials.length === 0) {
      console.log(`  \x1b[33m[警告] \x1b[0m没有找到材料类型的数据`);
      return [];
    }
    
    // 2. 智能排序
    const sortedMaterials = smartSortMaterials(materials);
    
    // 3. 生成清单项
    const materialList = [];
    let serialNo = 1;
    
    sortedMaterials.forEach(material => {
      if (material.isParent) {
        // 父级行不需要序号
        materialList.push(material);
      } else {
        const listItem = generateMaterialListItem(material, serialNo);
        materialList.push(listItem);
        serialNo++;
      }
    });
    
    // 4. 应用混凝土定价
    if (options.applyConcretePrice !== false) {
      applyConcretepricing(materialList, options.concreteBasePrice);
    }
    
    // 5. 计算汇总信息
    const summary = calculateMaterialSummary(materialList);
    
    console.log(`\x1b[36m[材料清单生成] \x1b[0m生成完成，共 \x1b[32m${materialList.length}\x1b[0m 个清单项`);
    console.log(`  \x1b[36m[汇总] \x1b[0m总金额: \x1b[32m${summary.totalAmount.toFixed(2)}\x1b[0m 元`);
    
    return {
      success: true,
      materialList,
      summary,
      statistics: {
        totalItems: materialList.length,
        steelItems: materialList.filter(item => item.category === "钢筋类").length,
        concreteItems: materialList.filter(item => item.category === "混凝土类").length,
        mortarItems: materialList.filter(item => item.category === "砂浆类").length,
        brickItems: materialList.filter(item => item.category === "砖类").length,
        otherItems: materialList.filter(item => item.category === "其他").length
      }
    };
    
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m材料清单生成失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      materialList: []
    };
  }
}

/**
 * 计算材料汇总信息
 * @param {Array} materialList - 材料清单数组
 * @returns {Object} 汇总信息
 */
function calculateMaterialSummary(materialList) {
  const summary = {
    totalAmount: 0,
    totalQuantity: 0,
    categoryTotals: {},
    itemCount: 0
  };
  
  materialList.forEach(item => {
    if (!item.isParent) {
      summary.totalAmount += item.amount || 0;
      summary.totalQuantity += item.quantity || 0;
      summary.itemCount++;
      
      // 按类别汇总
      if (!summary.categoryTotals[item.category]) {
        summary.categoryTotals[item.category] = {
          amount: 0,
          count: 0
        };
      }
      summary.categoryTotals[item.category].amount += item.amount || 0;
      summary.categoryTotals[item.category].count++;
    }
  });
  
  return summary;
}

module.exports = {
  createConcreteGroup,
  extractConcreteSpecification,
  calculateConcretePrice,
  smartSortMaterials,
  generateMaterialListItem,
  applyConcretepricing,
  generateMaterialList,
  calculateMaterialSummary
};
