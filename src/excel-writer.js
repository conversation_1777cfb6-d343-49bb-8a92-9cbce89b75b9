const ExcelJS = require("exceljs");
const path = require("path");
const {
  sortLevel1Items,
  sortLevel2Items,
  sortLevel3Items,
} = require("./sort-utils");
const { prepareLevel1Items, prepareLevel2Items } = require("./data-merger");

/**
 * 创建Excel工作簿和工作表
 * @returns {Object} 包含工作簿和工作表的对象
 */
function createWorkbook() {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("合并清单");

  // 设置表头
  worksheet.columns = [
    { header: "序号", key: "serialNo", width: 8 },
    { header: "清单编码", key: "billCode", width: 15 },
    { header: "清单名称", key: "billName", width: 40 },
    { header: "项目特征", key: "feature", width: 50 },
    { header: "工作内容", key: "workContent", width: 30 },
    { header: "计量单位", key: "unit", width: 10 },
    { header: "工程数量", key: "quantity", width: 12 },
    { header: "综合单价", key: "unitPrice", width: 12 },
    { header: "合价", key: "totalPrice", width: 15 },
  ];

  // 设置表头样式
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).alignment = { vertical: "middle", horizontal: "center" };
  worksheet.getRow(1).height = 20;

  // 设置大纲属性（用于展开/收起）
  worksheet.properties.outlineLevelRow = 3; // 最大大纲级别
  worksheet.properties.outlineLevelCol = 0;
  worksheet.properties.showOutlineRow = true; // 显示行大纲符号
  // 分级显示设置
  worksheet.properties.outlineProperties = {
    summaryBelow: false, // 将展开收起按钮显示在父级行
    summaryRight: false, // 将展开收起按钮显示在父级列
  };

  return { workbook, worksheet };
}

/**
 * 添加第1级数据行（分部工程）
 * @param {Object} worksheet - Excel工作表对象
 * @param {Object} item - 第1级数据项
 * @param {string} level1SerialNo - 第1级序号
 * @param {number} rowIndex - 当前行索引
 * @returns {Object} 包含行对象和更新后行索引的对象
 */
function addLevel1Row(worksheet, item, level1SerialNo, rowIndex) {
  const { divisionalWorkCode, divisionalWorkName } = item;

  worksheet.addRow({
    serialNo: level1SerialNo,
    billCode: divisionalWorkCode,
    billName: divisionalWorkName,
    feature: "",
    workContent: "",
    unit: "",
    quantity: "",
    unitPrice: "",
    totalPrice: "",
  });

  // 设置分部工程行的样式
  const divisionalWorkRow = worksheet.getRow(rowIndex);
  divisionalWorkRow.font = { bold: true };
  divisionalWorkRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  return { divisionalWorkRow, rowIndex: rowIndex + 1 };
}

/**
 * 添加第2级数据行（清单项）
 * @param {Object} worksheet - Excel工作表对象
 * @param {Object} item - 第2级数据项
 * @param {string} level2SerialNo - 第2级序号
 * @param {number} rowIndex - 当前行索引
 * @returns {Object} 包含行对象和更新后行索引的对象
 */
function addLevel2Row(worksheet, item, level2SerialNo, rowIndex) {
  const {
    billName,
    feature,
    workContent,
    unit,
    totalQuantity,
    maxUnitPrice,
    totalPrice,
    minBillCode,
  } = item;

  const level2Row = worksheet.addRow({
    serialNo: level2SerialNo,
    billCode: "", // 暂时不设置清单编码，后面从3级数据中获取最小的
    billName: billName,
    feature: feature,
    workContent: workContent,
    unit: unit,
    quantity: 0, // 暂时设置为0，后面计算
    unitPrice: 0, // 暂时设置为0，后面从3级数据中获取最大的
    totalPrice: 0, // 暂时设置为0，后面计算
  });

  // 更新第2级数据
  level2Row.getCell(2).value = minBillCode; // 清单编码设置为最小的
  level2Row.getCell(7).value = totalQuantity; // 工程数量
  level2Row.getCell(8).value = maxUnitPrice; // 综合单价设置为最大的
  level2Row.getCell(9).value = totalPrice; // 合价

  // 设置数字列的数据类型
  level2Row.getCell(7).numFmt = "0.00"; // 工程数量
  level2Row.getCell(8).numFmt = "0.00"; // 综合单价
  level2Row.getCell(9).numFmt = "0.00"; // 合价

  // 设置清单行的样式
  const billRow = worksheet.getRow(rowIndex);
  billRow.font = { bold: false };
  billRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFF5F5F5" },
  };

  return { billRow, rowIndex: rowIndex + 1 };
}

/**
 * 添加第3级数据行（工程项目）
 * @param {Object} worksheet - Excel工作表对象
 * @param {string} projectName - 项目名称
 * @param {Object} item - 清单项数据
 * @param {string} level3SerialNo - 第3级序号
 * @param {string} unit - 计量单位
 * @param {string} minBillCode - 最小清单编码
 * @param {number} maxUnitPrice - 最大单价
 * @param {number} rowIndex - 当前行索引
 * @returns {number} 更新后的行索引
 */
function addLevel3Row(
  worksheet,
  projectName,
  item,
  level3SerialNo,
  unit,
  minBillCode,
  maxUnitPrice,
  rowIndex
) {
  // 检查编码是否与第2级不一致
  const itemBillCode = item.billCode || "";
  const displayBillCode = itemBillCode !== minBillCode ? itemBillCode : "";

  // 检查单价是否与第2级不一致
  const itemUnitPrice = parseFloat(item.unitPrice) || 0;
  const itemQuantity = parseFloat(item.quantity) || 0;
  const itemTotalPrice = parseFloat(item.totalPrice) || 0;
  const displayUnitPrice = itemUnitPrice !== maxUnitPrice ? itemUnitPrice : "";

  // 添加第3级数据行
  const level3Row = worksheet.addRow({
    serialNo: level3SerialNo,
    billCode: displayBillCode, // 如果编码与第2级一致，则不显示
    billName: projectName, // 3级显示工程名称
    feature: "", // 3级不显示项目特征
    workContent: "", // 3级不显示工作内容
    unit: unit,
    quantity: itemQuantity,
    unitPrice: displayUnitPrice, // 如果单价与第2级一致，则不显示
    totalPrice: itemTotalPrice,
  });

  // 设置数字列的数据类型
  level3Row.getCell(7).numFmt = "0.00"; // 工程数量
  if (displayUnitPrice) {
    level3Row.getCell(8).numFmt = "0.00"; // 综合单价
  }
  level3Row.getCell(9).numFmt = "0.00"; // 合价

  // 如果编码与第2级不一致，设置浅红色背景
  if (displayBillCode) {
    level3Row.getCell(2).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFFFD6D6" }, // 浅红色
    };
  }

  // 如果单价与第2级不一致，设置浅红色背景
  if (displayUnitPrice) {
    level3Row.getCell(8).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFFFD6D6" }, // 浅红色
    };
  }

  // 设置分组级别（用于展开/收起）
  level3Row.outlineLevel = 2; // 第3级数据的大纲级别

  return rowIndex + 1;
}

/**
 * 设置单元格边框和对齐方式
 * @param {Object} worksheet - Excel工作表对象
 * @param {number} rowCount - 总行数
 */
function setCellStyles(worksheet, rowCount) {
  // 设置所有单元格的边框和对齐方式
  for (let i = 1; i <= rowCount; i++) {
    for (let j = 1; j <= 9; j++) {
      const cell = worksheet.getCell(i, j);
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };

      // 设置单元格对齐方式
      cell.alignment = { vertical: "middle", wrapText: true };
      if (j >= 6 && j <= 9) {
        // 数字列居中对齐
        cell.alignment.horizontal = "center";
      }
    }
  }
}

module.exports = {
  createWorkbook,
  addLevel1Row,
  addLevel2Row,
  addLevel3Row,
  setCellStyles,
};
