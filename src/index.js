const readline = require("readline");
const { processExcelFile } = require("./bill-merge");
const { calculateBill } = require("./bill-calculation");

/**
 * 显示功能菜单
 */
function showMenu() {
  console.clear();
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log("\x1b[32m           欢迎使用 清单工作助手\x1b[0m");
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
  console.log("");
  console.log("\x1b[33m请选择功能:\x1b[0m");
  console.log("\x1b[32m1. 清单合并\x1b[0m - 合并多个Excel清单文件");
  console.log("\x1b[32m2. 清单测算\x1b[0m - 清单价格测算和分析");
  console.log("\x1b[31m0. 退出程序\x1b[0m");
  console.log("");
  console.log(
    "\x1b[36m==================================================\x1b[0m"
  );
}

/**
 * 处理用户选择
 * @param {string} choice - 用户选择
 */
async function handleChoice(choice) {
  switch (choice.trim()) {
    case "1":
      console.log("\n\x1b[36m[启动] \x1b[33m清单合并功能...\x1b[0m");
      await processExcelFile();
      break;
    case "2":
      console.log("\n\x1b[36m[启动] \x1b[33m清单测算功能...\x1b[0m");
      await calculateBill();
      break;
    case "0":
      console.log("\n\x1b[32m感谢使用！再见！\x1b[0m");
      process.exit(0);
      break;
    default:
      console.log("\n\x1b[31m[错误] \x1b[0m无效选择，请重新输入！");
      setTimeout(main, 2000);
      return;
  }
}

/**
 * 主函数
 */
async function main() {
  showMenu();

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  rl.question("\x1b[33m请输入选项 (0-2): \x1b[0m", async (choice) => {
    rl.close();
    await handleChoice(choice);
  });
}

module.exports = {
  main,
};
