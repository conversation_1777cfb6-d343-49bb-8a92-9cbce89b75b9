# bill-work-mate

这是一个用于处理工程清单数据的工具，可以自动提取 Excel 数据，生成 JSON 文件，并将 JSON 文件中的数据合并重组，生成一个结构清晰的 Excel 文件。

## 功能特点

### 数据提取功能

1. 提取每个 sheet 页签中的表格数据
2. 对清单数据进行处理，拆分【项目名称】为【清单名称】、【项目特征】、【工作内容】
3. 提取分部工程信息，包括分部工程编码和分部工程名称
4. 将表头的【工程名称】作为 JSON 文件名，导出数据到 json 文件夹

### 数据合并功能

1. 序号的生成方式改为层级码，例如 1, 1.1, 1.1.1
2. 设置层级结构，使 3 级数据可以展开收起
3. 2 级分组默认不要清单编码和综合单价，先生成分组，然后从下级数据中找到最小的编码作为 2 级清单编码
4. 找到最大的综合单价作为 2 级综合单价
5. 如果 3 级的清单编码与 2 级不相同，显示并标记浅红色背景
6. 如果 3 级的综合单价与 2 级不相同，显示并标记浅红色背景
7. 工程数量、综合单价、合价列为数字列，而不是文本
8. 清单唯一标识改为"清单名称+项目特征"，而不是"清单编码+清单名称+项目特征"
9. 3 级的清单编码如果与 2 级一样，就不显示
10. 3 级的综合单价如果与 2 级一样，也不显示
11. 3 级数据按照特定规则排序：先按纯数字排序，然后按字母+数字排序，最后按字符顺序排序，并且正确处理类似"1#"、"2#"、"10#"这样的字符串排序

## 数据格式

### 输入 Excel 格式要求

- Excel 数据行中：A 列是序号，B 列是清单编码，C 列是项目名称，F 列是计量单位，G 列是工程数量，I 列是综合单价，J 列是合价。
- Excel 表头结构：1-4 行，第 1 行是标题，第 2 行第一个单元格是工程名称，第 3-4 行是合并行作为列头。

### 输出 JSON 数据格式

```json
[
  {
    "serialNo": "", // 序号
    "billCode": "", // 清单编码
    "name": "", // 原始项目名称
    "billName": "", // 清单名称（项目名称的第一行）
    "feature": "", // 项目特征
    "workContent": "", // 工作内容
    "unit": "", // 计量单位
    "quantity": 0, // 工程数量
    "unitPrice": 0, // 综合单价
    "totalPrice": 0, // 合价
    "divisionalWorkCode": "", // 分部工程编码
    "divisionalWorkName": "" // 分部工程名称
  }
]
```

### 输出 Excel 格式

- 第 1 级：分部工程（清单编码取值来源为分部工程编码，清单名称取值来源为分部工程名称）
- 第 2 级：清单（取清单项对应的数据，其中工程数量是 3 级汇总求和上来的，综合单价取第 3 级最大值）
- 第 3 级：各工程对应的同清单分组内的信息，并标记与第 2 级不一致的编码和单价

## 开发者使用方法

### 环境要求

- Node.js 18.x 或更高版本
- pnpm 包管理工具

### 开发步骤

1. 克隆仓库：`git clone <仓库地址>`
2. 安装依赖：`pnpm install`
3. 将原始清单 Excel 文件放在项目的 input 目录下
4. 运行程序：`pnpm start`
5. 查看生成的 JSON 文件和 output 目录下的合并清单 Excel 文件

### 构建可执行文件

1. 运行构建命令：`pnpm run build`
2. 构建完成后，可执行文件将生成在 `dist` 目录下
3. 将以下文件打包给用户：
   - bill-work-mate.exe
   - README.txt
   - 示例/目录 (可选)

## 最终用户使用方法

1. 将原始清单 Excel 文件放在程序的 input 目录下
2. 双击运行"bill-work-mate.exe"程序
3. 程序会自动处理 Excel 文件，并在 output 目录下生成合并清单 Excel 文件
4. 处理完成后，按回车键退出程序

## 技术栈

- Node.js - JavaScript 运行环境
- ExcelJS - Excel 文件处理库
- pkg - Node.js 应用打包工具

## 版本历史

- v1.2.2 - 更新程序名称为"bill-work-mate"
- v1.2.1 - 修复了清单排序问题，现在清单行先按照清单名称排序，再按照清单编码排序
- v1.2.0 - 添加了对 1 级和 2 级数据的排序功能，优化了表头列的自动识别
- v1.1.0 - 支持同时处理多个项目的清单文件，更新程序名称为"一键合并清单"
- v1.0.0 - 初始版本，实现基本功能
